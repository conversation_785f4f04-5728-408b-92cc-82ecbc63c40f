#!/usr/bin/env python3
"""
量化投资平台服务启动和测试脚本
专门处理服务启动问题并进行真实用户测试

这个脚本将：
1. 检查和启动必要的服务
2. 等待服务完全启动
3. 进行真实用户测试
4. 生成详细的问题报告
"""

import asyncio
import json
import time
import subprocess
import requests
import os
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志（避免emoji字符）
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp/puppeteer/service_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ServiceStartupAndTest:
    """服务启动和测试器"""
    
    def __init__(self):
        self.session_id = str(int(time.time()))
        self.frontend_url = 'http://localhost:5173'
        self.backend_url = 'http://localhost:8000'
        self.browser: Browser = None
        self.page: Page = None
        
        self.test_results = {
            'session_info': {
                'session_id': self.session_id,
                'start_time': datetime.now().isoformat(),
                'test_type': '服务启动和用户测试'
            },
            'service_startup': {
                'frontend_startup_attempts': 0,
                'backend_startup_attempts': 0,
                'frontend_running': False,
                'backend_running': False,
                'startup_issues': []
            },
            'user_testing': {
                'scenarios_completed': 0,
                'issues_found': [],
                'screenshots_taken': []
            },
            'discovered_problems': [],
            'recommendations': []
        }
        
        self.screenshots_dir = Path('mcp/puppeteer/screenshots_service_test')
        self.screenshots_dir.mkdir(exist_ok=True)
    
    async def run_complete_test(self):
        """运行完整的服务启动和测试"""
        logger.info(f"开始服务启动和测试 - 会话ID: {self.session_id}")
        
        try:
            # 步骤1: 检查当前服务状态
            await self._check_current_services()
            
            # 步骤2: 尝试启动服务
            await self._attempt_service_startup()
            
            # 步骤3: 等待服务稳定
            await self._wait_for_services()
            
            # 步骤4: 进行用户测试
            if self.test_results['service_startup']['frontend_running']:
                await self._run_user_tests()
            else:
                logger.error("前端服务未运行，跳过用户测试")
                self.test_results['discovered_problems'].append({
                    'type': 'critical',
                    'issue': '前端服务无法启动',
                    'impact': '用户无法访问平台',
                    'recommendation': '检查前端配置和依赖'
                })
            
            # 步骤5: 生成报告
            await self._generate_final_report()
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            self.test_results['test_error'] = str(e)
        finally:
            if self.browser:
                await self.browser.close()
    
    async def _check_current_services(self):
        """检查当前服务状态"""
        logger.info("检查当前服务状态...")
        
        # 检查前端服务
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                self.test_results['service_startup']['frontend_running'] = True
                logger.info("前端服务已运行")
            else:
                logger.info(f"前端服务响应异常: {response.status_code}")
        except Exception as e:
            logger.info(f"前端服务未运行: {e}")
        
        # 检查后端服务
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            if response.status_code == 200:
                self.test_results['service_startup']['backend_running'] = True
                logger.info("后端服务已运行")
            else:
                logger.info(f"后端服务响应异常: {response.status_code}")
        except Exception as e:
            logger.info(f"后端服务未运行: {e}")
    
    async def _attempt_service_startup(self):
        """尝试启动服务"""
        logger.info("尝试启动服务...")
        
        # 启动前端服务
        if not self.test_results['service_startup']['frontend_running']:
            await self._start_frontend_service()
        
        # 启动后端服务
        if not self.test_results['service_startup']['backend_running']:
            await self._start_backend_service()
    
    async def _start_frontend_service(self):
        """启动前端服务"""
        logger.info("启动前端服务...")
        
        frontend_dir = Path('frontend')
        if not frontend_dir.exists():
            self.test_results['service_startup']['startup_issues'].append(
                "前端目录不存在"
            )
            return
        
        try:
            # 尝试多种启动方式
            startup_commands = [
                ['npm', 'run', 'dev'],
                ['npx', 'vite', '--mode', 'development'],
                ['npx', 'vite']
            ]
            
            for cmd in startup_commands:
                try:
                    self.test_results['service_startup']['frontend_startup_attempts'] += 1
                    logger.info(f"尝试启动命令: {' '.join(cmd)}")
                    
                    # 启动进程
                    process = subprocess.Popen(
                        cmd,
                        cwd=str(frontend_dir),
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        shell=True
                    )
                    
                    # 等待一段时间让服务启动
                    await asyncio.sleep(10)
                    
                    # 检查服务是否启动成功
                    if await self._check_service_health(self.frontend_url):
                        self.test_results['service_startup']['frontend_running'] = True
                        logger.info("前端服务启动成功")
                        return
                    else:
                        logger.info(f"启动命令 {' '.join(cmd)} 未成功")
                        
                except Exception as e:
                    logger.error(f"启动命令失败 {' '.join(cmd)}: {e}")
                    self.test_results['service_startup']['startup_issues'].append(
                        f"前端启动失败: {str(e)}"
                    )
            
            logger.error("所有前端启动尝试都失败了")
            
        except Exception as e:
            logger.error(f"前端服务启动异常: {e}")
            self.test_results['service_startup']['startup_issues'].append(
                f"前端启动异常: {str(e)}"
            )
    
    async def _start_backend_service(self):
        """启动后端服务"""
        logger.info("启动后端服务...")
        
        backend_dir = Path('backend')
        if not backend_dir.exists():
            self.test_results['service_startup']['startup_issues'].append(
                "后端目录不存在"
            )
            return
        
        try:
            # 查找可能的启动文件
            startup_files = ['app.py', 'main.py', 'server.py', 'run.py']
            
            for filename in startup_files:
                filepath = backend_dir / filename
                if filepath.exists():
                    try:
                        self.test_results['service_startup']['backend_startup_attempts'] += 1
                        logger.info(f"尝试启动后端: {filename}")
                        
                        process = subprocess.Popen(
                            ['python', filename],
                            cwd=str(backend_dir),
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            shell=True
                        )
                        
                        # 等待服务启动
                        await asyncio.sleep(5)
                        
                        # 检查服务健康状态
                        if await self._check_service_health(f"{self.backend_url}/health"):
                            self.test_results['service_startup']['backend_running'] = True
                            logger.info("后端服务启动成功")
                            return
                        else:
                            logger.info(f"后端启动文件 {filename} 未成功")
                            
                    except Exception as e:
                        logger.error(f"后端启动失败 {filename}: {e}")
                        self.test_results['service_startup']['startup_issues'].append(
                            f"后端启动失败 {filename}: {str(e)}"
                        )
            
            logger.error("未找到可用的后端启动文件")
            self.test_results['service_startup']['startup_issues'].append(
                "未找到后端启动文件"
            )
            
        except Exception as e:
            logger.error(f"后端服务启动异常: {e}")
            self.test_results['service_startup']['startup_issues'].append(
                f"后端启动异常: {str(e)}"
            )
    
    async def _check_service_health(self, url: str) -> bool:
        """检查服务健康状态"""
        try:
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    async def _wait_for_services(self):
        """等待服务稳定"""
        logger.info("等待服务稳定...")
        
        max_wait_time = 30  # 最大等待30秒
        check_interval = 2  # 每2秒检查一次
        
        for i in range(max_wait_time // check_interval):
            await asyncio.sleep(check_interval)
            
            # 重新检查服务状态
            frontend_ok = await self._check_service_health(self.frontend_url)
            backend_ok = await self._check_service_health(f"{self.backend_url}/health")
            
            self.test_results['service_startup']['frontend_running'] = frontend_ok
            self.test_results['service_startup']['backend_running'] = backend_ok
            
            logger.info(f"服务状态检查 {i+1}: 前端={frontend_ok}, 后端={backend_ok}")
            
            if frontend_ok:
                logger.info("前端服务已稳定运行")
                break
        
        if not self.test_results['service_startup']['frontend_running']:
            logger.error("前端服务在等待时间内未能稳定运行")
    
    async def _run_user_tests(self):
        """运行用户测试"""
        logger.info("开始用户测试...")
        
        try:
            # 设置浏览器
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(
                headless=False,
                slow_mo=500
            )
            
            context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            self.page = await context.new_page()
            
            # 监听页面错误
            self.page.on('pageerror', self._handle_page_error)
            
            # 测试场景1: 基本页面访问
            await self._test_basic_access()
            
            # 测试场景2: 页面导航
            await self._test_navigation()
            
            # 测试场景3: 功能模块检查
            await self._test_modules()
            
        except Exception as e:
            logger.error(f"用户测试失败: {e}")
            self.test_results['user_testing']['issues_found'].append({
                'type': 'test_error',
                'message': str(e)
            })
    
    def _handle_page_error(self, error):
        """处理页面错误"""
        self.test_results['user_testing']['issues_found'].append({
            'type': 'page_error',
            'message': str(error),
            'timestamp': datetime.now().isoformat()
        })
    
    async def _test_basic_access(self):
        """测试基本页面访问"""
        logger.info("测试基本页面访问...")
        
        try:
            await self.page.goto(self.frontend_url, wait_until='networkidle')
            
            # 截图
            screenshot_path = self.screenshots_dir / f"basic_access_{self.session_id}.png"
            await self.page.screenshot(path=str(screenshot_path), full_page=True)
            self.test_results['user_testing']['screenshots_taken'].append(str(screenshot_path))
            
            # 检查页面标题
            title = await self.page.title()
            logger.info(f"页面标题: {title}")
            
            # 检查页面内容
            content = await self.page.content()
            if len(content) > 1000:
                logger.info("页面内容正常")
                self.test_results['user_testing']['scenarios_completed'] += 1
            else:
                logger.warning("页面内容过少")
                self.test_results['user_testing']['issues_found'].append({
                    'type': 'content_issue',
                    'message': '页面内容过少'
                })
            
        except Exception as e:
            logger.error(f"基本访问测试失败: {e}")
            self.test_results['user_testing']['issues_found'].append({
                'type': 'access_error',
                'message': str(e)
            })
    
    async def _test_navigation(self):
        """测试页面导航"""
        logger.info("测试页面导航...")
        
        try:
            # 查找导航元素
            nav_elements = await self.page.query_selector_all('nav a, .nav-item, .menu-item')
            logger.info(f"发现导航元素: {len(nav_elements)}个")
            
            if len(nav_elements) > 0:
                self.test_results['user_testing']['scenarios_completed'] += 1
            else:
                self.test_results['user_testing']['issues_found'].append({
                    'type': 'navigation_issue',
                    'message': '未发现导航元素'
                })
            
        except Exception as e:
            logger.error(f"导航测试失败: {e}")
            self.test_results['user_testing']['issues_found'].append({
                'type': 'navigation_error',
                'message': str(e)
            })
    
    async def _test_modules(self):
        """测试功能模块"""
        logger.info("测试功能模块...")
        
        try:
            # 检查常见的功能模块元素
            module_selectors = [
                '.dashboard', '.market', '.trading', '.strategy', 
                '.portfolio', '.risk', '.chart', '.table'
            ]
            
            found_modules = 0
            for selector in module_selectors:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    found_modules += 1
            
            logger.info(f"发现功能模块: {found_modules}个")
            
            if found_modules > 0:
                self.test_results['user_testing']['scenarios_completed'] += 1
            else:
                self.test_results['user_testing']['issues_found'].append({
                    'type': 'module_issue',
                    'message': '未发现功能模块'
                })
            
        except Exception as e:
            logger.error(f"模块测试失败: {e}")
            self.test_results['user_testing']['issues_found'].append({
                'type': 'module_error',
                'message': str(e)
            })
    
    async def _generate_final_report(self):
        """生成最终报告"""
        logger.info("生成最终报告...")
        
        self.test_results['session_info']['end_time'] = datetime.now().isoformat()
        
        # 分析问题并生成建议
        self._analyze_and_recommend()
        
        # 保存JSON报告
        report_file = f"mcp/puppeteer/service_startup_test_{self.session_id}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # 生成Markdown报告
        await self._generate_markdown_report()
        
        logger.info(f"测试报告已生成: {report_file}")
        self._print_summary()
    
    def _analyze_and_recommend(self):
        """分析问题并生成建议"""
        recommendations = []
        
        # 服务启动问题分析
        if not self.test_results['service_startup']['frontend_running']:
            recommendations.append({
                'priority': 'critical',
                'category': '服务启动',
                'issue': '前端服务无法启动',
                'recommendation': '检查Node.js版本、依赖安装和配置文件'
            })
        
        if not self.test_results['service_startup']['backend_running']:
            recommendations.append({
                'priority': 'high',
                'category': '服务启动',
                'issue': '后端服务无法启动',
                'recommendation': '检查Python环境、依赖安装和数据库配置'
            })
        
        # 用户测试问题分析
        issues_count = len(self.test_results['user_testing']['issues_found'])
        if issues_count > 0:
            recommendations.append({
                'priority': 'medium',
                'category': '用户体验',
                'issue': f'发现{issues_count}个用户体验问题',
                'recommendation': '修复页面错误，完善功能模块'
            })
        
        self.test_results['recommendations'] = recommendations
    
    async def _generate_markdown_report(self):
        """生成Markdown报告"""
        frontend_status = "运行中" if self.test_results['service_startup']['frontend_running'] else "未运行"
        backend_status = "运行中" if self.test_results['service_startup']['backend_running'] else "未运行"
        
        report_content = f"""# 量化投资平台服务启动和用户测试报告

## 测试概述
- **会话ID**: {self.session_id}
- **测试时间**: {self.test_results['session_info']['start_time']}
- **测试类型**: 服务启动和用户体验测试

## 服务状态
- **前端服务**: {frontend_status}
- **后端服务**: {backend_status}

## 启动尝试统计
- **前端启动尝试**: {self.test_results['service_startup']['frontend_startup_attempts']}次
- **后端启动尝试**: {self.test_results['service_startup']['backend_startup_attempts']}次

## 用户测试结果
- **完成场景**: {self.test_results['user_testing']['scenarios_completed']}个
- **发现问题**: {len(self.test_results['user_testing']['issues_found'])}个
- **截图数量**: {len(self.test_results['user_testing']['screenshots_taken'])}张

## 发现的问题
"""
        
        # 添加启动问题
        for issue in self.test_results['service_startup']['startup_issues']:
            report_content += f"- **启动问题**: {issue}\n"
        
        # 添加用户测试问题
        for issue in self.test_results['user_testing']['issues_found']:
            report_content += f"- **{issue['type']}**: {issue['message']}\n"
        
        # 添加建议
        if self.test_results['recommendations']:
            report_content += "\n## 改进建议\n\n"
            for rec in self.test_results['recommendations']:
                report_content += f"### {rec['issue']} ({rec['priority'].upper()})\n"
                report_content += f"- **类别**: {rec['category']}\n"
                report_content += f"- **建议**: {rec['recommendation']}\n\n"
        
        report_content += f"\n---\n**报告生成时间**: {datetime.now().isoformat()}\n"
        
        # 保存Markdown报告
        md_file = f"mcp/puppeteer/service_startup_test_{self.session_id}.md"
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
    
    def _print_summary(self):
        """打印测试总结"""
        print("\n" + "="*80)
        print("量化投资平台服务启动和用户测试完成")
        print("="*80)
        print(f"前端服务: {'运行中' if self.test_results['service_startup']['frontend_running'] else '未运行'}")
        print(f"后端服务: {'运行中' if self.test_results['service_startup']['backend_running'] else '未运行'}")
        print(f"用户测试场景: {self.test_results['user_testing']['scenarios_completed']}个")
        print(f"发现问题: {len(self.test_results['user_testing']['issues_found'])}个")
        print(f"改进建议: {len(self.test_results['recommendations'])}条")
        print("="*80)


async def main():
    """主函数"""
    print("启动量化投资平台服务启动和用户测试")
    print("目标: 解决服务启动问题并进行用户体验测试")
    print("-" * 60)
    
    tester = ServiceStartupAndTest()
    await tester.run_complete_test()
    
    print("\n测试完成！请查看生成的报告。")


if __name__ == "__main__":
    asyncio.run(main())
