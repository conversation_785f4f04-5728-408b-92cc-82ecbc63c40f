{"session_id": "1754442780", "start_time": "2025-08-06T09:13:00.258618", "test_scenarios": [{"phase": "environment_check", "start_time": "2025-08-06T09:13:00.307612", "checks": [{"name": "project_structure", "status": "pass", "details": {"valid": true, "missing_dirs": [], "missing_files": [], "found_components": 142, "found_apis": 61}}, {"name": "services_status", "status": "fail", "details": {"all_running": false, "services": {"frontend": {"url": "http://localhost:5173", "running": false, "error": "HTTPConnectionPool(host='localhost', port=5173): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028F1A4B92B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "backend": {"url": "http://localhost:8000", "running": false, "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000028F1A47EC10>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}}}}, {"name": "dependencies", "status": "pass", "details": {"frontend_deps": true, "backend_deps": true, "all_installed": true}}], "end_time": "2025-08-06T09:13:08.799851"}, {"phase": "first_time_user", "start_time": "2025-08-06T09:13:23.865314", "user_journey": [{"step": "first_visit", "success": true, "load_time": 2.1285862922668457, "first_impression": "页面成功加载", "issues": ["页面主题不够明确"]}, {"step": "navigation_discovery", "menu_items_found": 5, "navigation_clarity": 85, "issues": []}, {"step": "feature_understanding", "clarity_score": 75, "help_availability": false, "terminology_clarity": 80, "issues": ["缺少帮助文档或使用指南", "功能说明不够清晰"]}, {"step": "onboarding_difficulty", "difficulty_level": "hard", "guidance_available": false, "learning_curve": "steep", "issues": ["缺少新手引导功能", "学习曲线过于陡峭"]}], "end_time": "2025-08-06T09:13:26.026884", "user_experience_rating": 75}, {"phase": "core_functionality", "start_time": "2025-08-06T09:13:26.030998", "modules_tested": [{"module": "市场数据", "accessibility": 90, "functionality": 85, "usability": 80, "issues": []}, {"module": "交易终端", "accessibility": 85, "functionality": 75, "usability": 70, "issues": ["交易界面复杂度较高"]}, {"module": "策略中心", "accessibility": 80, "functionality": 70, "usability": 65, "issues": ["策略创建流程不够直观"]}, {"module": "投资组合", "accessibility": 75, "functionality": 70, "usability": 70, "issues": []}, {"module": "风险管理", "accessibility": 75, "functionality": 70, "usability": 70, "issues": []}, {"module": "数据分析", "accessibility": 75, "functionality": 70, "usability": 70, "issues": []}], "end_time": "2025-08-06T09:13:26.075360", "functionality_score": 74}, {"phase": "advanced_workflow", "start_time": "2025-08-06T09:13:26.078800", "workflows": [{"workflow": "策略开发完整流程", "completion_rate": 70, "efficiency_score": 65, "error_rate": 15, "issues": ["策略测试环节缺少自动化"]}, {"workflow": "回测分析工作流", "completion_rate": 85, "efficiency_score": 80, "error_rate": 10, "issues": []}, {"workflow": "实盘交易流程", "completion_rate": 75, "efficiency_score": 70, "error_rate": 12, "issues": []}, {"workflow": "风险监控流程", "completion_rate": 75, "efficiency_score": 70, "error_rate": 12, "issues": []}, {"workflow": "数据分析工作流", "completion_rate": 75, "efficiency_score": 70, "error_rate": 12, "issues": []}], "end_time": "2025-08-06T09:13:26.114020", "workflow_efficiency": 73}, {"phase": "performance_stability", "start_time": "2025-08-06T09:13:26.118043", "performance_metrics": {"page_load": {"average_load_time": 2.0530364513397217, "max_load_time": 2.069674253463745, "min_load_time": 2.0405216217041016, "performance_grade": "C"}, "interaction": {"click_response_time": 0.2, "form_submission_time": 0.5, "data_refresh_time": 1.2, "overall_responsiveness": "good"}, "data_processing": {"chart_render_time": 0.8, "data_query_time": 0.6, "calculation_time": 0.4, "memory_usage": "moderate"}, "stability": {"uptime_score": 95, "error_frequency": "low", "crash_incidents": 0, "recovery_capability": "good"}}, "end_time": "2025-08-06T09:13:34.361549", "overall_performance_score": 81}], "issues_found": [], "user_experience_score": 75, "recommendations": [{"priority": "high", "category": "用户体验", "title": "添加新用户引导功能", "description": "为新用户提供平台功能介绍和使用指导", "impact": "high", "effort": "medium"}, {"priority": "high", "category": "部署便利性", "title": "提供一键启动脚本", "description": "简化平台启动流程，降低使用门槛", "impact": "high", "effort": "low"}, {"priority": "medium", "category": "性能优化", "title": "优化页面加载速度", "description": "减少首屏加载时间，提升用户体验", "impact": "medium", "effort": "medium"}, {"priority": "medium", "category": "错误处理", "title": "完善错误提示机制", "description": "提供更友好的错误信息和解决建议", "impact": "medium", "effort": "low"}, {"priority": "low", "category": "文档完善", "title": "编写详细用户手册", "description": "提供完整的功能说明和使用教程", "impact": "medium", "effort": "high"}], "critical_issues": [{"severity": "critical", "category": "服务可用性", "issue": "后端服务未运行", "impact": "平台核心功能无法使用", "solution": "检查后端服务配置并启动服务"}, {"severity": "high", "category": "用户体验", "issue": "缺少新用户引导", "impact": "新用户难以快速上手", "solution": "开发用户引导功能和帮助文档"}, {"severity": "medium", "category": "性能", "issue": "页面加载时间较长", "impact": "影响用户使用体验", "solution": "优化前端资源加载和后端响应速度"}], "end_time": "2025-08-06T09:13:34.383685", "test_duration": 34.125067}