#!/usr/bin/env python3
"""
智能量化投资平台测试
自动发现和启动服务，进行全面的真实用户测试

这个脚本将：
1. 智能发现项目结构和启动文件
2. 自动启动前后端服务
3. 进行深度的真实用户体验测试
4. 生成详细的问题分析和改进建议
"""

import asyncio
import json
import time
import subprocess
import requests
import os
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp/puppeteer/intelligent_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class IntelligentPlatformTest:
    """智能平台测试器"""
    
    def __init__(self):
        self.session_id = str(int(time.time()))
        self.frontend_url = 'http://localhost:5173'
        self.backend_url = 'http://localhost:8000'
        self.browser: Browser = None
        self.page: Page = None
        
        self.test_results = {
            'session_info': {
                'session_id': self.session_id,
                'start_time': datetime.now().isoformat(),
                'test_type': '智能平台深度测试'
            },
            'project_discovery': {
                'frontend_files': [],
                'backend_files': [],
                'startup_candidates': []
            },
            'service_management': {
                'frontend_status': 'unknown',
                'backend_status': 'unknown',
                'startup_attempts': []
            },
            'user_experience_test': {
                'page_analysis': {},
                'navigation_test': {},
                'functionality_test': {},
                'performance_metrics': {},
                'accessibility_check': {}
            },
            'issues_discovered': [],
            'recommendations': [],
            'screenshots': []
        }
        
        self.screenshots_dir = Path('mcp/puppeteer/screenshots_intelligent')
        self.screenshots_dir.mkdir(exist_ok=True)
    
    async def run_intelligent_test(self):
        """运行智能测试"""
        logger.info(f"开始智能平台测试 - 会话ID: {self.session_id}")
        
        try:
            # 阶段1: 项目发现和分析
            await self._discover_project_structure()
            
            # 阶段2: 智能服务管理
            await self._intelligent_service_management()
            
            # 阶段3: 深度用户体验测试
            await self._deep_user_experience_test()
            
            # 阶段4: 问题分析和建议生成
            await self._analyze_and_recommend()
            
            # 阶段5: 生成综合报告
            await self._generate_comprehensive_report()
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            self.test_results['test_error'] = str(e)
        finally:
            if self.browser:
                await self.browser.close()
    
    async def _discover_project_structure(self):
        """发现项目结构"""
        logger.info("发现项目结构...")
        
        # 发现前端文件
        frontend_dir = Path('frontend')
        if frontend_dir.exists():
            self.test_results['project_discovery']['frontend_files'] = [
                str(f) for f in frontend_dir.rglob('*.json') if 'package.json' in f.name
            ]
        
        # 发现后端文件
        backend_dir = Path('backend')
        if backend_dir.exists():
            # 查找所有可能的启动文件
            startup_patterns = ['main.py', 'app.py', 'server.py', 'run.py', 'start.py']
            for pattern in startup_patterns:
                files = list(backend_dir.rglob(pattern))
                for file in files:
                    self.test_results['project_discovery']['startup_candidates'].append({
                        'file': str(file),
                        'relative_path': str(file.relative_to(backend_dir)),
                        'size': file.stat().st_size if file.exists() else 0
                    })
        
        logger.info(f"发现后端启动候选文件: {len(self.test_results['project_discovery']['startup_candidates'])}个")
    
    async def _intelligent_service_management(self):
        """智能服务管理"""
        logger.info("智能服务管理...")
        
        # 检查当前服务状态
        await self._check_services()
        
        # 如果前端未运行，尝试启动
        if self.test_results['service_management']['frontend_status'] != 'running':
            await self._start_frontend_intelligent()
        
        # 如果后端未运行，尝试启动
        if self.test_results['service_management']['backend_status'] != 'running':
            await self._start_backend_intelligent()
        
        # 等待服务稳定
        await self._wait_for_service_stability()
    
    async def _check_services(self):
        """检查服务状态"""
        # 检查前端
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                self.test_results['service_management']['frontend_status'] = 'running'
                logger.info("前端服务正在运行")
            else:
                self.test_results['service_management']['frontend_status'] = 'error'
        except:
            self.test_results['service_management']['frontend_status'] = 'not_running'
        
        # 检查后端
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            if response.status_code == 200:
                self.test_results['service_management']['backend_status'] = 'running'
                logger.info("后端服务正在运行")
            else:
                self.test_results['service_management']['backend_status'] = 'error'
        except:
            self.test_results['service_management']['backend_status'] = 'not_running'
    
    async def _start_frontend_intelligent(self):
        """智能启动前端"""
        logger.info("智能启动前端服务...")
        
        frontend_dir = Path('frontend')
        if not frontend_dir.exists():
            return
        
        # 尝试不同的启动命令
        commands = [
            ['npm', 'run', 'dev'],
            ['yarn', 'dev'],
            ['npx', 'vite'],
            ['npx', 'vite', '--mode', 'development']
        ]
        
        for cmd in commands:
            try:
                logger.info(f"尝试启动命令: {' '.join(cmd)}")
                process = subprocess.Popen(
                    cmd,
                    cwd=str(frontend_dir),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    shell=True
                )
                
                self.test_results['service_management']['startup_attempts'].append({
                    'service': 'frontend',
                    'command': ' '.join(cmd),
                    'timestamp': datetime.now().isoformat()
                })
                
                # 等待启动
                await asyncio.sleep(10)
                
                # 检查是否成功
                try:
                    response = requests.get(self.frontend_url, timeout=5)
                    if response.status_code == 200:
                        self.test_results['service_management']['frontend_status'] = 'running'
                        logger.info("前端服务启动成功")
                        return
                except:
                    pass
                
            except Exception as e:
                logger.error(f"前端启动失败: {e}")
    
    async def _start_backend_intelligent(self):
        """智能启动后端"""
        logger.info("智能启动后端服务...")
        
        # 按优先级排序启动候选文件
        candidates = self.test_results['project_discovery']['startup_candidates']
        
        # 优先级：app/main.py > main.py > app.py > 其他
        priority_order = ['app/main.py', 'main.py', 'app.py', 'server.py']
        
        sorted_candidates = []
        for priority in priority_order:
            for candidate in candidates:
                if priority in candidate['relative_path']:
                    sorted_candidates.append(candidate)
        
        # 添加剩余的候选文件
        for candidate in candidates:
            if candidate not in sorted_candidates:
                sorted_candidates.append(candidate)
        
        for candidate in sorted_candidates:
            try:
                file_path = Path(candidate['file'])
                working_dir = file_path.parent
                
                logger.info(f"尝试启动: {candidate['relative_path']}")
                
                process = subprocess.Popen(
                    ['python', file_path.name],
                    cwd=str(working_dir),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    shell=True
                )
                
                self.test_results['service_management']['startup_attempts'].append({
                    'service': 'backend',
                    'file': candidate['relative_path'],
                    'timestamp': datetime.now().isoformat()
                })
                
                # 等待启动
                await asyncio.sleep(8)
                
                # 检查是否成功
                try:
                    response = requests.get(f"{self.backend_url}/health", timeout=5)
                    if response.status_code == 200:
                        self.test_results['service_management']['backend_status'] = 'running'
                        logger.info(f"后端服务启动成功: {candidate['relative_path']}")
                        return
                except:
                    pass
                
            except Exception as e:
                logger.error(f"后端启动失败 {candidate['relative_path']}: {e}")
    
    async def _wait_for_service_stability(self):
        """等待服务稳定"""
        logger.info("等待服务稳定...")
        
        for i in range(10):  # 最多等待20秒
            await asyncio.sleep(2)
            await self._check_services()
            
            if self.test_results['service_management']['frontend_status'] == 'running':
                logger.info("服务已稳定")
                break
    
    async def _deep_user_experience_test(self):
        """深度用户体验测试"""
        logger.info("开始深度用户体验测试...")
        
        if self.test_results['service_management']['frontend_status'] != 'running':
            logger.error("前端服务未运行，跳过用户体验测试")
            return
        
        try:
            # 启动浏览器
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(
                headless=False,
                slow_mo=300,
                args=['--disable-web-security']
            )
            
            context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            self.page = await context.new_page()
            
            # 监听页面事件
            self.page.on('console', self._handle_console)
            self.page.on('pageerror', self._handle_page_error)
            
            # 执行各种测试
            await self._test_page_analysis()
            await self._test_navigation()
            await self._test_functionality()
            await self._test_performance()
            await self._test_accessibility()
            
        except Exception as e:
            logger.error(f"用户体验测试失败: {e}")
            self.test_results['issues_discovered'].append({
                'type': 'test_error',
                'severity': 'high',
                'message': f'用户体验测试失败: {str(e)}'
            })
    
    def _handle_console(self, msg):
        """处理控制台消息"""
        if msg.type in ['error', 'warning']:
            self.test_results['issues_discovered'].append({
                'type': f'console_{msg.type}',
                'severity': 'medium' if msg.type == 'warning' else 'high',
                'message': msg.text,
                'timestamp': datetime.now().isoformat()
            })
    
    def _handle_page_error(self, error):
        """处理页面错误"""
        self.test_results['issues_discovered'].append({
            'type': 'page_error',
            'severity': 'critical',
            'message': str(error),
            'timestamp': datetime.now().isoformat()
        })
    
    async def _test_page_analysis(self):
        """页面分析测试"""
        logger.info("进行页面分析...")
        
        try:
            await self.page.goto(self.frontend_url, wait_until='networkidle')
            
            # 截图
            screenshot_path = self.screenshots_dir / f"page_analysis_{self.session_id}.png"
            await self.page.screenshot(path=str(screenshot_path), full_page=True)
            self.test_results['screenshots'].append(str(screenshot_path))
            
            # 分析页面
            title = await self.page.title()
            content = await self.page.content()
            
            # 统计页面元素
            buttons = await self.page.query_selector_all('button')
            links = await self.page.query_selector_all('a')
            inputs = await self.page.query_selector_all('input')
            forms = await self.page.query_selector_all('form')
            
            self.test_results['user_experience_test']['page_analysis'] = {
                'title': title,
                'content_length': len(content),
                'elements': {
                    'buttons': len(buttons),
                    'links': len(links),
                    'inputs': len(inputs),
                    'forms': len(forms)
                }
            }
            
            logger.info(f"页面分析完成 - 标题: {title}, 按钮: {len(buttons)}个")
            
        except Exception as e:
            logger.error(f"页面分析失败: {e}")
    
    async def _test_navigation(self):
        """导航测试"""
        logger.info("进行导航测试...")
        
        try:
            # 查找导航元素
            nav_selectors = [
                'nav', '.nav', '.navbar', '.navigation',
                '.menu', '.sidebar', '.header-nav'
            ]
            
            nav_elements = []
            for selector in nav_selectors:
                elements = await self.page.query_selector_all(selector)
                nav_elements.extend(elements)
            
            # 查找导航链接
            nav_links = await self.page.query_selector_all('nav a, .nav a, .menu a')
            
            self.test_results['user_experience_test']['navigation_test'] = {
                'nav_containers': len(nav_elements),
                'nav_links': len(nav_links),
                'navigation_present': len(nav_elements) > 0 or len(nav_links) > 0
            }
            
            if not self.test_results['user_experience_test']['navigation_test']['navigation_present']:
                self.test_results['issues_discovered'].append({
                    'type': 'navigation_missing',
                    'severity': 'medium',
                    'message': '未发现明显的导航结构'
                })
            
            logger.info(f"导航测试完成 - 导航容器: {len(nav_elements)}个, 导航链接: {len(nav_links)}个")
            
        except Exception as e:
            logger.error(f"导航测试失败: {e}")
    
    async def _test_functionality(self):
        """功能测试"""
        logger.info("进行功能测试...")
        
        try:
            # 查找功能模块
            module_selectors = [
                '.dashboard', '.market', '.trading', '.strategy',
                '.portfolio', '.risk', '.chart', '.table',
                '.panel', '.widget', '.card'
            ]
            
            found_modules = {}
            for selector in module_selectors:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    found_modules[selector] = len(elements)
            
            self.test_results['user_experience_test']['functionality_test'] = {
                'modules_found': found_modules,
                'total_modules': sum(found_modules.values()),
                'has_interactive_elements': len(found_modules) > 0
            }
            
            logger.info(f"功能测试完成 - 发现模块: {len(found_modules)}种类型")
            
        except Exception as e:
            logger.error(f"功能测试失败: {e}")
    
    async def _test_performance(self):
        """性能测试"""
        logger.info("进行性能测试...")
        
        try:
            # 测试页面加载时间
            start_time = time.time()
            await self.page.reload(wait_until='networkidle')
            load_time = time.time() - start_time
            
            # 获取性能指标
            performance = await self.page.evaluate('''() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                return {
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                    firstPaint: performance.getEntriesByType('paint').find(p => p.name === 'first-paint')?.startTime || 0
                };
            }''')
            
            self.test_results['user_experience_test']['performance_metrics'] = {
                'page_load_time': load_time,
                'dom_content_loaded': performance.get('domContentLoaded', 0),
                'load_complete': performance.get('loadComplete', 0),
                'first_paint': performance.get('firstPaint', 0)
            }
            
            # 性能评估
            if load_time > 3:
                self.test_results['issues_discovered'].append({
                    'type': 'performance_slow',
                    'severity': 'medium',
                    'message': f'页面加载时间过长: {load_time:.2f}秒'
                })
            
            logger.info(f"性能测试完成 - 加载时间: {load_time:.2f}秒")
            
        except Exception as e:
            logger.error(f"性能测试失败: {e}")
    
    async def _test_accessibility(self):
        """可访问性测试"""
        logger.info("进行可访问性测试...")
        
        try:
            # 检查基本的可访问性元素
            alt_texts = await self.page.query_selector_all('img[alt]')
            labels = await self.page.query_selector_all('label')
            headings = await self.page.query_selector_all('h1, h2, h3, h4, h5, h6')
            
            self.test_results['user_experience_test']['accessibility_check'] = {
                'images_with_alt': len(alt_texts),
                'form_labels': len(labels),
                'heading_structure': len(headings)
            }
            
            logger.info(f"可访问性测试完成 - 标题: {len(headings)}个, 标签: {len(labels)}个")
            
        except Exception as e:
            logger.error(f"可访问性测试失败: {e}")
    
    async def _analyze_and_recommend(self):
        """分析和建议"""
        logger.info("分析问题并生成建议...")
        
        recommendations = []
        
        # 服务状态分析
        if self.test_results['service_management']['backend_status'] != 'running':
            recommendations.append({
                'priority': 'critical',
                'category': '服务可用性',
                'issue': '后端服务未运行',
                'recommendation': '启动后端API服务，确保数据功能可用',
                'impact': '核心功能无法使用'
            })
        
        # 用户体验分析
        nav_test = self.test_results['user_experience_test'].get('navigation_test', {})
        if not nav_test.get('navigation_present', False):
            recommendations.append({
                'priority': 'high',
                'category': '用户体验',
                'issue': '导航结构不明显',
                'recommendation': '添加清晰的导航菜单和页面结构',
                'impact': '用户难以浏览和使用平台'
            })
        
        # 性能分析
        perf_metrics = self.test_results['user_experience_test'].get('performance_metrics', {})
        load_time = perf_metrics.get('page_load_time', 0)
        if load_time > 2:
            recommendations.append({
                'priority': 'medium',
                'category': '性能优化',
                'issue': f'页面加载时间较长({load_time:.2f}秒)',
                'recommendation': '优化前端资源加载，实施代码分割和缓存策略',
                'impact': '影响用户体验和使用效率'
            })
        
        self.test_results['recommendations'] = recommendations
    
    async def _generate_comprehensive_report(self):
        """生成综合报告"""
        logger.info("生成综合报告...")
        
        self.test_results['session_info']['end_time'] = datetime.now().isoformat()
        
        # 保存JSON报告
        report_file = f"mcp/puppeteer/intelligent_test_{self.session_id}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # 生成Markdown报告
        await self._generate_markdown_report()
        
        logger.info(f"测试报告已生成: {report_file}")
        self._print_summary()
    
    async def _generate_markdown_report(self):
        """生成Markdown报告"""
        frontend_status = self.test_results['service_management']['frontend_status']
        backend_status = self.test_results['service_management']['backend_status']
        
        report_content = f"""# 智能量化投资平台深度测试报告

## 测试概述
- **会话ID**: {self.session_id}
- **测试时间**: {self.test_results['session_info']['start_time']}
- **测试类型**: 智能平台深度测试

## 项目发现
- **后端启动候选文件**: {len(self.test_results['project_discovery']['startup_candidates'])}个
- **服务启动尝试**: {len(self.test_results['service_management']['startup_attempts'])}次

## 服务状态
- **前端服务**: {frontend_status}
- **后端服务**: {backend_status}

## 用户体验测试结果
"""
        
        # 添加页面分析结果
        page_analysis = self.test_results['user_experience_test'].get('page_analysis', {})
        if page_analysis:
            report_content += f"""
### 页面分析
- **页面标题**: {page_analysis.get('title', 'N/A')}
- **内容长度**: {page_analysis.get('content_length', 0)}字符
- **交互元素**: 按钮{page_analysis.get('elements', {}).get('buttons', 0)}个, 链接{page_analysis.get('elements', {}).get('links', 0)}个
"""
        
        # 添加性能指标
        perf_metrics = self.test_results['user_experience_test'].get('performance_metrics', {})
        if perf_metrics:
            report_content += f"""
### 性能指标
- **页面加载时间**: {perf_metrics.get('page_load_time', 0):.2f}秒
- **DOM加载时间**: {perf_metrics.get('dom_content_loaded', 0):.2f}毫秒
"""
        
        # 添加发现的问题
        issues = self.test_results['issues_discovered']
        if issues:
            report_content += f"\n## 发现的问题 ({len(issues)}个)\n\n"
            for issue in issues:
                report_content += f"- **{issue['type']}** ({issue['severity'].upper()}): {issue['message']}\n"
        
        # 添加改进建议
        recommendations = self.test_results['recommendations']
        if recommendations:
            report_content += f"\n## 改进建议 ({len(recommendations)}条)\n\n"
            for rec in recommendations:
                report_content += f"### {rec['issue']} ({rec['priority'].upper()})\n"
                report_content += f"- **类别**: {rec['category']}\n"
                report_content += f"- **建议**: {rec['recommendation']}\n"
                report_content += f"- **影响**: {rec['impact']}\n\n"
        
        report_content += f"\n---\n**报告生成时间**: {datetime.now().isoformat()}\n"
        report_content += f"**测试工具**: Puppeteer MCP + Playwright (智能模式)\n"
        
        # 保存Markdown报告
        md_file = f"mcp/puppeteer/intelligent_test_{self.session_id}.md"
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
    
    def _print_summary(self):
        """打印测试总结"""
        frontend_status = self.test_results['service_management']['frontend_status']
        backend_status = self.test_results['service_management']['backend_status']
        issues_count = len(self.test_results['issues_discovered'])
        recommendations_count = len(self.test_results['recommendations'])
        
        print("\n" + "="*80)
        print("智能量化投资平台深度测试完成")
        print("="*80)
        print(f"前端服务: {frontend_status}")
        print(f"后端服务: {backend_status}")
        print(f"发现问题: {issues_count}个")
        print(f"改进建议: {recommendations_count}条")
        print(f"截图数量: {len(self.test_results['screenshots'])}张")
        print("="*80)


async def main():
    """主函数"""
    print("启动智能量化投资平台深度测试")
    print("特点: 自动发现、智能启动、深度分析")
    print("-" * 60)
    
    tester = IntelligentPlatformTest()
    await tester.run_intelligent_test()
    
    print("\n智能测试完成！请查看生成的详细报告。")


if __name__ == "__main__":
    asyncio.run(main())
