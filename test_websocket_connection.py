#!/usr/bin/env python3
"""
WebSocket连接测试脚本
用于测试后端WebSocket端点是否正常工作
"""

import asyncio
import websockets
import json
from datetime import datetime

async def test_websocket():
    """测试WebSocket连接"""
    uri = "ws://localhost:8000/api/v1/ws?token=dev-token-for-testing"
    
    try:
        print(f"🔌 正在连接到: {uri}")
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功!")
            
            # 等待欢迎消息
            try:
                welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 收到欢迎消息: {welcome_msg}")
            except asyncio.TimeoutError:
                print("⚠️ 未收到欢迎消息")
            
            # 发送测试消息
            test_message = {
                "type": "test",
                "message": "Hello from test client",
                "timestamp": datetime.now().isoformat()
            }
            
            print(f"📤 发送测试消息: {test_message}")
            await websocket.send(json.dumps(test_message))
            
            # 等待回复
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 收到回复: {response}")
            except asyncio.TimeoutError:
                print("⚠️ 未收到回复")
            
            # 等待心跳消息
            print("⏳ 等待心跳消息...")
            try:
                heartbeat = await asyncio.wait_for(websocket.recv(), timeout=35.0)
                print(f"💓 收到心跳: {heartbeat}")
            except asyncio.TimeoutError:
                print("⚠️ 未收到心跳消息")
                
            print("✅ WebSocket测试完成")
            
    except websockets.exceptions.ConnectionRefused:
        print("❌ 连接被拒绝 - 请确保后端服务正在运行")
    except websockets.exceptions.InvalidURI:
        print("❌ 无效的URI")
    except Exception as e:
        print(f"❌ 连接失败: {e}")

if __name__ == "__main__":
    print("🚀 开始WebSocket连接测试")
    asyncio.run(test_websocket())
