{"session_id": "1754442972", "start_time": "2025-08-06T09:16:12.452333", "mcp_tools_used": [], "user_scenarios": [{"name": "新用户首次访问", "start_time": "2025-08-06T09:16:41.877287", "success": true, "issues": [], "screenshots": [], "performance": {}, "page_loaded": true, "load_time": 2.068934202194214, "navigation_visible": false, "content_meaningful": true, "end_time": "2025-08-06T09:16:43.946306"}, {"name": "浏览市场数据", "start_time": "2025-08-06T09:16:43.950912", "success": true, "issues": [], "screenshots": [], "performance": {}, "market_page_accessible": false, "data_charts_visible": false, "real_time_updates": false, "end_time": "2025-08-06T09:16:43.951222"}, {"name": "查看交易终端", "start_time": "2025-08-06T09:16:43.955192", "success": true, "issues": [], "screenshots": [], "performance": {}, "terminal_accessible": false, "trading_forms_present": false, "order_book_visible": false, "end_time": "2025-08-06T09:16:43.955547"}, {"name": "探索策略中心", "start_time": "2025-08-06T09:16:43.959327", "success": true, "issues": [], "screenshots": [], "performance": {}, "strategy_page_accessible": false, "strategy_list_visible": false, "strategy_creation_available": false, "end_time": "2025-08-06T09:16:43.959588"}, {"name": "检查投资组合", "start_time": "2025-08-06T09:16:43.962942", "success": true, "issues": [], "screenshots": [], "performance": {}, "portfolio_accessible": false, "holdings_displayed": false, "performance_charts": false, "end_time": "2025-08-06T09:16:43.963174"}, {"name": "测试风险管理功能", "start_time": "2025-08-06T09:16:43.966245", "success": true, "issues": [], "screenshots": [], "performance": {}, "risk_page_accessible": false, "risk_metrics_visible": false, "alerts_configured": false, "end_time": "2025-08-06T09:16:43.966469"}], "issues_discovered": [{"severity": "critical", "category": "服务可用性", "issue": "后端服务未运行", "impact": "平台核心功能无法使用", "recommendation": "启动后端API服务器或检查数据库连接"}], "screenshots_taken": [], "performance_metrics": {"page_load_times": [{"url": "http://localhost:5173", "load_time": 2.062103509902954, "status": "success"}, {"url": "http://localhost:5173/#/market", "load_time": 2.068702220916748, "status": "success"}, {"url": "http://localhost:5173/#/trading", "load_time": 2.037771224975586, "status": "success"}, {"url": "http://localhost:5173/#/strategy", "load_time": 2.029280185699463, "status": "success"}], "api_response_times": [{"url": "http://localhost:8000/health", "response_time": 4.072801351547241, "status": "error", "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EB4CFF7F00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, {"url": "http://localhost:8000/api/market/data", "response_time": 4.099689722061157, "status": "error", "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/market/data (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EB4CFF78A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, {"url": "http://localhost:8000/api/user/profile", "response_time": 4.076435804367065, "status": "error", "error": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/user/profile (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001EB4CFF7BD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}], "memory_usage": "unknown", "cpu_usage": "unknown"}, "user_experience_rating": 76, "services_status": {"frontend_running": true, "backend_running": false}, "performance_score": 75, "end_time": "2025-08-06T09:17:04.431394"}