#!/usr/bin/env python3
"""
专注前端的真实用户深度测试
由于前端服务已运行，专注于深度的用户体验测试

这个脚本将：
1. 深度分析前端页面结构和功能
2. 模拟真实用户的完整使用流程
3. 发现用户体验问题和改进机会
4. 生成详细的测试报告和截图
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright, Page, Browser
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp/puppeteer/focused_frontend_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FocusedFrontendTest:
    """专注前端的深度测试器"""
    
    def __init__(self):
        self.session_id = str(int(time.time()))
        self.platform_url = 'http://localhost:5173'
        self.browser: Browser = None
        self.page: Page = None
        
        self.test_results = {
            'session_info': {
                'session_id': self.session_id,
                'start_time': datetime.now().isoformat(),
                'test_type': '专注前端深度用户体验测试',
                'platform_url': self.platform_url
            },
            'page_structure_analysis': {},
            'user_journey_tests': [],
            'interaction_tests': [],
            'visual_analysis': {},
            'performance_analysis': {},
            'accessibility_analysis': {},
            'issues_discovered': [],
            'user_experience_score': 0,
            'recommendations': [],
            'screenshots': []
        }
        
        self.screenshots_dir = Path('mcp/puppeteer/screenshots_focused')
        self.screenshots_dir.mkdir(exist_ok=True)
        self.screenshot_counter = 0
    
    async def run_focused_test(self):
        """运行专注的前端测试"""
        logger.info(f"开始专注前端深度测试 - 会话ID: {self.session_id}")
        
        try:
            # 设置浏览器环境
            await self._setup_browser()
            
            # 阶段1: 页面结构深度分析
            await self._analyze_page_structure()
            
            # 阶段2: 用户旅程测试
            await self._test_user_journeys()
            
            # 阶段3: 交互功能测试
            await self._test_interactions()
            
            # 阶段4: 视觉和布局分析
            await self._analyze_visual_layout()
            
            # 阶段5: 性能深度分析
            await self._analyze_performance()
            
            # 阶段6: 可访问性评估
            await self._analyze_accessibility()
            
            # 阶段7: 综合分析和评分
            await self._comprehensive_analysis()
            
            # 生成最终报告
            await self._generate_final_report()
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            self.test_results['test_error'] = str(e)
        finally:
            if self.browser:
                await self.browser.close()
    
    async def _setup_browser(self):
        """设置浏览器环境"""
        logger.info("设置浏览器环境...")
        
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,
            slow_mo=200,  # 减慢操作，模拟真实用户
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security'
            ]
        )
        
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        self.page = await context.new_page()
        
        # 监听页面事件
        self.page.on('console', self._handle_console_message)
        self.page.on('pageerror', self._handle_page_error)
        self.page.on('response', self._handle_response)
        
        logger.info("浏览器环境设置完成")
    
    def _handle_console_message(self, msg):
        """处理控制台消息"""
        if msg.type in ['error', 'warning']:
            self.test_results['issues_discovered'].append({
                'type': f'console_{msg.type}',
                'severity': 'high' if msg.type == 'error' else 'medium',
                'message': msg.text,
                'timestamp': datetime.now().isoformat(),
                'source': 'console'
            })
    
    def _handle_page_error(self, error):
        """处理页面错误"""
        self.test_results['issues_discovered'].append({
            'type': 'page_error',
            'severity': 'critical',
            'message': str(error),
            'timestamp': datetime.now().isoformat(),
            'source': 'page'
        })
    
    def _handle_response(self, response):
        """处理HTTP响应"""
        if response.status >= 400:
            self.test_results['issues_discovered'].append({
                'type': 'http_error',
                'severity': 'high' if response.status >= 500 else 'medium',
                'message': f'HTTP {response.status}: {response.url}',
                'timestamp': datetime.now().isoformat(),
                'source': 'network'
            })
    
    async def _take_screenshot(self, name: str, description: str = ""):
        """截图并保存"""
        self.screenshot_counter += 1
        filename = f"test_{self.session_id}_{self.screenshot_counter:03d}_{name}_{int(time.time())}.png"
        screenshot_path = self.screenshots_dir / filename
        
        await self.page.screenshot(path=str(screenshot_path), full_page=True)
        
        screenshot_info = {
            'filename': filename,
            'path': str(screenshot_path),
            'description': description,
            'timestamp': datetime.now().isoformat()
        }
        
        self.test_results['screenshots'].append(screenshot_info)
        logger.info(f"截图已保存: {filename} - {description}")
        return screenshot_info
    
    async def _analyze_page_structure(self):
        """页面结构深度分析"""
        logger.info("进行页面结构深度分析...")
        
        try:
            # 访问主页
            await self.page.goto(self.platform_url, wait_until='networkidle')
            await self._take_screenshot('homepage_loaded', '主页加载完成')
            
            # 基本页面信息
            title = await self.page.title()
            url = self.page.url
            
            # 分析页面元素
            elements_analysis = await self._analyze_page_elements()
            
            # 分析页面布局
            layout_analysis = await self._analyze_page_layout()
            
            # 分析内容结构
            content_analysis = await self._analyze_content_structure()
            
            self.test_results['page_structure_analysis'] = {
                'basic_info': {
                    'title': title,
                    'url': url,
                    'load_timestamp': datetime.now().isoformat()
                },
                'elements': elements_analysis,
                'layout': layout_analysis,
                'content': content_analysis
            }
            
            logger.info(f"页面结构分析完成 - 标题: {title}")
            
        except Exception as e:
            logger.error(f"页面结构分析失败: {e}")
            self.test_results['issues_discovered'].append({
                'type': 'analysis_error',
                'severity': 'high',
                'message': f'页面结构分析失败: {str(e)}',
                'source': 'test'
            })
    
    async def _analyze_page_elements(self):
        """分析页面元素"""
        elements = {}
        
        # 基础元素统计
        element_types = {
            'buttons': 'button',
            'links': 'a',
            'inputs': 'input',
            'forms': 'form',
            'images': 'img',
            'headings': 'h1, h2, h3, h4, h5, h6',
            'paragraphs': 'p',
            'divs': 'div',
            'spans': 'span'
        }
        
        for name, selector in element_types.items():
            count = len(await self.page.query_selector_all(selector))
            elements[name] = count
        
        # 特殊元素分析
        nav_elements = await self.page.query_selector_all('nav, .nav, .navbar, .navigation')
        menu_elements = await self.page.query_selector_all('.menu, .sidebar')
        chart_elements = await self.page.query_selector_all('canvas, .chart, .echarts, svg')
        table_elements = await self.page.query_selector_all('table, .table')
        
        elements.update({
            'navigation_containers': len(nav_elements),
            'menu_containers': len(menu_elements),
            'chart_elements': len(chart_elements),
            'table_elements': len(table_elements)
        })
        
        return elements
    
    async def _analyze_page_layout(self):
        """分析页面布局"""
        layout = {}
        
        try:
            # 获取页面尺寸
            page_size = await self.page.evaluate('''() => {
                return {
                    width: document.documentElement.scrollWidth,
                    height: document.documentElement.scrollHeight,
                    viewportWidth: window.innerWidth,
                    viewportHeight: window.innerHeight
                };
            }''')
            
            # 检查响应式设计
            responsive_elements = await self.page.query_selector_all('[class*="responsive"], [class*="mobile"], [class*="tablet"]')
            
            # 检查布局容器
            layout_containers = await self.page.query_selector_all('.container, .wrapper, .layout, .grid, .flex')
            
            layout = {
                'page_dimensions': page_size,
                'responsive_elements': len(responsive_elements),
                'layout_containers': len(layout_containers),
                'has_responsive_design': len(responsive_elements) > 0
            }
            
        except Exception as e:
            logger.error(f"布局分析失败: {e}")
        
        return layout
    
    async def _analyze_content_structure(self):
        """分析内容结构"""
        content = {}
        
        try:
            # 获取页面文本内容
            text_content = await self.page.evaluate('() => document.body.innerText')
            
            # 分析标题结构
            headings = await self.page.query_selector_all('h1, h2, h3, h4, h5, h6')
            heading_texts = []
            for heading in headings:
                text = await heading.inner_text()
                tag_name = await heading.evaluate('el => el.tagName')
                heading_texts.append({'level': tag_name, 'text': text})
            
            # 检查主要内容区域
            main_content = await self.page.query_selector_all('main, .main, .content, .dashboard')
            
            content = {
                'text_length': len(text_content),
                'has_meaningful_content': len(text_content) > 100,
                'heading_structure': heading_texts,
                'main_content_areas': len(main_content)
            }
            
        except Exception as e:
            logger.error(f"内容结构分析失败: {e}")
        
        return content
    
    async def _test_user_journeys(self):
        """测试用户旅程"""
        logger.info("测试用户旅程...")
        
        # 用户旅程1: 新用户首次访问
        journey1 = await self._test_first_time_user_journey()
        self.test_results['user_journey_tests'].append(journey1)
        
        # 用户旅程2: 功能探索
        journey2 = await self._test_feature_exploration_journey()
        self.test_results['user_journey_tests'].append(journey2)
        
        # 用户旅程3: 导航测试
        journey3 = await self._test_navigation_journey()
        self.test_results['user_journey_tests'].append(journey3)
    
    async def _test_first_time_user_journey(self):
        """测试新用户首次访问旅程"""
        journey = {
            'name': '新用户首次访问',
            'start_time': datetime.now().isoformat(),
            'steps': [],
            'success': True,
            'issues': []
        }
        
        try:
            # 步骤1: 页面加载和第一印象
            await self.page.reload(wait_until='networkidle')
            await asyncio.sleep(2)  # 模拟用户观察时间
            await self._take_screenshot('first_impression', '新用户第一印象')
            
            journey['steps'].append({
                'step': '页面加载',
                'success': True,
                'description': '页面成功加载'
            })
            
            # 步骤2: 寻找主要功能
            main_features = await self.page.query_selector_all('.dashboard, .market, .trading, .strategy')
            if len(main_features) == 0:
                journey['issues'].append('未发现明显的主要功能区域')
            
            journey['steps'].append({
                'step': '功能发现',
                'success': len(main_features) > 0,
                'description': f'发现{len(main_features)}个主要功能区域'
            })
            
            # 步骤3: 寻找导航
            nav_elements = await self.page.query_selector_all('nav a, .nav a, .menu a')
            if len(nav_elements) == 0:
                journey['issues'].append('未发现明显的导航元素')
            
            journey['steps'].append({
                'step': '导航发现',
                'success': len(nav_elements) > 0,
                'description': f'发现{len(nav_elements)}个导航链接'
            })
            
        except Exception as e:
            journey['success'] = False
            journey['error'] = str(e)
            logger.error(f"新用户旅程测试失败: {e}")
        
        journey['end_time'] = datetime.now().isoformat()
        return journey
    
    async def _test_feature_exploration_journey(self):
        """测试功能探索旅程"""
        journey = {
            'name': '功能探索',
            'start_time': datetime.now().isoformat(),
            'steps': [],
            'success': True,
            'features_found': []
        }
        
        try:
            # 查找可点击的功能元素
            clickable_elements = await self.page.query_selector_all('button, a, .clickable, [onclick]')
            
            # 尝试点击一些元素（安全的）
            safe_selectors = ['button:not([type="submit"])', 'a[href^="#"]']
            
            for selector in safe_selectors:
                elements = await self.page.query_selector_all(selector)
                if elements and len(elements) > 0:
                    try:
                        # 点击第一个元素
                        await elements[0].click()
                        await asyncio.sleep(1)
                        await self._take_screenshot('feature_click', f'点击{selector}元素')
                        
                        journey['features_found'].append({
                            'selector': selector,
                            'clickable': True
                        })
                    except Exception as e:
                        logger.warning(f"点击元素失败 {selector}: {e}")
            
            journey['steps'].append({
                'step': '功能交互测试',
                'success': len(journey['features_found']) > 0,
                'description': f'成功交互{len(journey["features_found"])}个功能元素'
            })
            
        except Exception as e:
            journey['success'] = False
            journey['error'] = str(e)
            logger.error(f"功能探索旅程测试失败: {e}")
        
        journey['end_time'] = datetime.now().isoformat()
        return journey
    
    async def _test_navigation_journey(self):
        """测试导航旅程"""
        journey = {
            'name': '导航测试',
            'start_time': datetime.now().isoformat(),
            'steps': [],
            'success': True,
            'navigation_paths': []
        }
        
        try:
            # 查找导航链接
            nav_links = await self.page.query_selector_all('nav a, .nav a, .menu a')
            
            for i, link in enumerate(nav_links[:3]):  # 只测试前3个链接
                try:
                    href = await link.get_attribute('href')
                    text = await link.inner_text()
                    
                    if href and not href.startswith('http'):  # 只测试内部链接
                        await link.click()
                        await asyncio.sleep(2)
                        await self._take_screenshot('navigation', f'导航到{text}')
                        
                        journey['navigation_paths'].append({
                            'text': text,
                            'href': href,
                            'success': True
                        })
                except Exception as e:
                    logger.warning(f"导航测试失败 {i}: {e}")
            
            journey['steps'].append({
                'step': '导航链接测试',
                'success': len(journey['navigation_paths']) > 0,
                'description': f'成功测试{len(journey["navigation_paths"])}个导航路径'
            })
            
        except Exception as e:
            journey['success'] = False
            journey['error'] = str(e)
            logger.error(f"导航旅程测试失败: {e}")
        
        journey['end_time'] = datetime.now().isoformat()
        return journey
    
    async def _test_interactions(self):
        """测试交互功能"""
        logger.info("测试交互功能...")
        
        interactions = {
            'button_interactions': [],
            'form_interactions': [],
            'hover_effects': [],
            'keyboard_navigation': []
        }
        
        try:
            # 测试按钮交互
            buttons = await self.page.query_selector_all('button')
            for i, button in enumerate(buttons[:5]):  # 测试前5个按钮
                try:
                    text = await button.inner_text()
                    await button.hover()
                    await asyncio.sleep(0.5)
                    
                    interactions['button_interactions'].append({
                        'index': i,
                        'text': text,
                        'hoverable': True
                    })
                except Exception as e:
                    logger.warning(f"按钮交互测试失败 {i}: {e}")
            
            # 测试表单交互
            inputs = await self.page.query_selector_all('input')
            for i, input_elem in enumerate(inputs[:3]):  # 测试前3个输入框
                try:
                    input_type = await input_elem.get_attribute('type')
                    placeholder = await input_elem.get_attribute('placeholder')
                    
                    if input_type in ['text', 'email', 'search']:
                        await input_elem.click()
                        await input_elem.fill('测试输入')
                        await asyncio.sleep(0.5)
                        await input_elem.clear()
                        
                        interactions['form_interactions'].append({
                            'index': i,
                            'type': input_type,
                            'placeholder': placeholder,
                            'interactive': True
                        })
                except Exception as e:
                    logger.warning(f"表单交互测试失败 {i}: {e}")
            
        except Exception as e:
            logger.error(f"交互测试失败: {e}")
        
        self.test_results['interaction_tests'] = interactions
    
    async def _analyze_visual_layout(self):
        """分析视觉和布局"""
        logger.info("分析视觉和布局...")
        
        visual_analysis = {}
        
        try:
            # 截图用于视觉分析
            await self._take_screenshot('visual_analysis', '视觉布局分析')
            
            # 分析颜色和样式
            styles = await self.page.evaluate('''() => {
                const computedStyle = getComputedStyle(document.body);
                return {
                    backgroundColor: computedStyle.backgroundColor,
                    color: computedStyle.color,
                    fontFamily: computedStyle.fontFamily,
                    fontSize: computedStyle.fontSize
                };
            }''')
            
            # 检查视觉层次
            headings_count = len(await self.page.query_selector_all('h1, h2, h3, h4, h5, h6'))
            
            visual_analysis = {
                'body_styles': styles,
                'heading_hierarchy': headings_count > 0,
                'has_visual_structure': headings_count > 0
            }
            
        except Exception as e:
            logger.error(f"视觉分析失败: {e}")
        
        self.test_results['visual_analysis'] = visual_analysis
    
    async def _analyze_performance(self):
        """分析性能"""
        logger.info("分析性能...")
        
        performance = {}
        
        try:
            # 测试页面加载性能
            start_time = time.time()
            await self.page.reload(wait_until='networkidle')
            load_time = time.time() - start_time
            
            # 获取浏览器性能指标
            perf_metrics = await self.page.evaluate('''() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                const paint = performance.getEntriesByType('paint');
                
                return {
                    domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
                    loadComplete: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
                    firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
                    firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0
                };
            }''')
            
            performance = {
                'page_load_time': load_time,
                'browser_metrics': perf_metrics,
                'performance_grade': 'A' if load_time < 1 else 'B' if load_time < 2 else 'C' if load_time < 3 else 'D'
            }
            
            # 性能问题检测
            if load_time > 3:
                self.test_results['issues_discovered'].append({
                    'type': 'performance_slow',
                    'severity': 'medium',
                    'message': f'页面加载时间过长: {load_time:.2f}秒',
                    'source': 'performance'
                })
            
        except Exception as e:
            logger.error(f"性能分析失败: {e}")
        
        self.test_results['performance_analysis'] = performance
    
    async def _analyze_accessibility(self):
        """分析可访问性"""
        logger.info("分析可访问性...")
        
        accessibility = {}
        
        try:
            # 检查基本可访问性元素
            images_with_alt = await self.page.query_selector_all('img[alt]')
            images_without_alt = await self.page.query_selector_all('img:not([alt])')
            form_labels = await self.page.query_selector_all('label')
            headings = await self.page.query_selector_all('h1, h2, h3, h4, h5, h6')
            
            accessibility = {
                'images_with_alt': len(images_with_alt),
                'images_without_alt': len(images_without_alt),
                'form_labels': len(form_labels),
                'heading_structure': len(headings),
                'accessibility_score': 0
            }
            
            # 计算可访问性评分
            score = 100
            if len(images_without_alt) > 0:
                score -= 20
            if len(form_labels) == 0:
                score -= 15
            if len(headings) == 0:
                score -= 25
            
            accessibility['accessibility_score'] = max(0, score)
            
            # 可访问性问题检测
            if len(images_without_alt) > 0:
                self.test_results['issues_discovered'].append({
                    'type': 'accessibility_images',
                    'severity': 'medium',
                    'message': f'{len(images_without_alt)}张图片缺少alt属性',
                    'source': 'accessibility'
                })
            
        except Exception as e:
            logger.error(f"可访问性分析失败: {e}")
        
        self.test_results['accessibility_analysis'] = accessibility
    
    async def _comprehensive_analysis(self):
        """综合分析和评分"""
        logger.info("进行综合分析和评分...")
        
        # 计算用户体验评分
        score_components = {
            'page_structure': 0,
            'user_journeys': 0,
            'interactions': 0,
            'performance': 0,
            'accessibility': 0
        }
        
        # 页面结构评分 (25%)
        elements = self.test_results['page_structure_analysis'].get('elements', {})
        if elements.get('buttons', 0) > 0 and elements.get('links', 0) > 0:
            score_components['page_structure'] = 85
        else:
            score_components['page_structure'] = 60
        
        # 用户旅程评分 (25%)
        successful_journeys = len([j for j in self.test_results['user_journey_tests'] if j.get('success', False)])
        total_journeys = len(self.test_results['user_journey_tests'])
        if total_journeys > 0:
            score_components['user_journeys'] = (successful_journeys / total_journeys) * 100
        else:
            score_components['user_journeys'] = 50
        
        # 交互评分 (20%)
        interactions = self.test_results['interaction_tests']
        if interactions.get('button_interactions') or interactions.get('form_interactions'):
            score_components['interactions'] = 80
        else:
            score_components['interactions'] = 40
        
        # 性能评分 (15%)
        perf_grade = self.test_results['performance_analysis'].get('performance_grade', 'D')
        grade_scores = {'A': 95, 'B': 85, 'C': 70, 'D': 50}
        score_components['performance'] = grade_scores.get(perf_grade, 50)
        
        # 可访问性评分 (15%)
        score_components['accessibility'] = self.test_results['accessibility_analysis'].get('accessibility_score', 50)
        
        # 计算加权总分
        weights = {'page_structure': 0.25, 'user_journeys': 0.25, 'interactions': 0.20, 'performance': 0.15, 'accessibility': 0.15}
        total_score = sum(score_components[component] * weights[component] for component in score_components)
        
        self.test_results['user_experience_score'] = round(total_score, 1)
        self.test_results['score_breakdown'] = score_components
        
        # 生成改进建议
        self._generate_recommendations()
        
        logger.info(f"综合分析完成 - 用户体验评分: {total_score:.1f}/100")
    
    def _generate_recommendations(self):
        """生成改进建议"""
        recommendations = []
        
        # 基于发现的问题生成建议
        issues_by_type = {}
        for issue in self.test_results['issues_discovered']:
            issue_type = issue['type']
            if issue_type not in issues_by_type:
                issues_by_type[issue_type] = []
            issues_by_type[issue_type].append(issue)
        
        # 控制台错误建议
        if 'console_error' in issues_by_type:
            recommendations.append({
                'priority': 'high',
                'category': '错误修复',
                'issue': f'发现{len(issues_by_type["console_error"])}个控制台错误',
                'recommendation': '修复JavaScript错误，确保页面功能正常',
                'impact': '影响用户体验和功能可用性'
            })
        
        # 性能建议
        perf_score = self.test_results['score_breakdown'].get('performance', 0)
        if perf_score < 70:
            recommendations.append({
                'priority': 'medium',
                'category': '性能优化',
                'issue': '页面加载性能需要改进',
                'recommendation': '优化资源加载，实施缓存策略，减少页面大小',
                'impact': '提升用户体验和使用效率'
            })
        
        # 可访问性建议
        acc_score = self.test_results['accessibility_analysis'].get('accessibility_score', 0)
        if acc_score < 80:
            recommendations.append({
                'priority': 'medium',
                'category': '可访问性',
                'issue': '可访问性需要改进',
                'recommendation': '添加图片alt属性，完善表单标签，优化标题结构',
                'impact': '提升无障碍访问体验'
            })
        
        # 用户体验建议
        ux_score = self.test_results['user_experience_score']
        if ux_score < 80:
            recommendations.append({
                'priority': 'medium',
                'category': '用户体验',
                'issue': '整体用户体验有改进空间',
                'recommendation': '优化页面布局，增强交互反馈，完善导航结构',
                'impact': '提升用户满意度和使用效率'
            })
        
        self.test_results['recommendations'] = recommendations
    
    async def _generate_final_report(self):
        """生成最终报告"""
        logger.info("生成最终报告...")
        
        self.test_results['session_info']['end_time'] = datetime.now().isoformat()
        self.test_results['session_info']['duration'] = (
            datetime.fromisoformat(self.test_results['session_info']['end_time']) -
            datetime.fromisoformat(self.test_results['session_info']['start_time'])
        ).total_seconds()
        
        # 保存JSON报告
        report_file = f"mcp/puppeteer/focused_frontend_test_{self.session_id}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # 生成Markdown报告
        await self._generate_markdown_report()
        
        logger.info(f"测试报告已生成: {report_file}")
        self._print_summary()
    
    async def _generate_markdown_report(self):
        """生成Markdown报告"""
        ux_score = self.test_results['user_experience_score']
        
        # 确定评级
        if ux_score >= 90:
            grade = "优秀 ⭐⭐⭐⭐⭐"
        elif ux_score >= 80:
            grade = "良好 ⭐⭐⭐⭐☆"
        elif ux_score >= 70:
            grade = "一般 ⭐⭐⭐☆☆"
        elif ux_score >= 60:
            grade = "较差 ⭐⭐☆☆☆"
        else:
            grade = "很差 ⭐☆☆☆☆"
        
        report_content = f"""# 专注前端深度用户体验测试报告

## 测试概述
- **会话ID**: {self.session_id}
- **测试时间**: {self.test_results['session_info']['start_time']}
- **测试类型**: 专注前端深度用户体验测试
- **平台URL**: {self.test_results['session_info']['platform_url']}
- **用户体验评分**: {ux_score}/100
- **评级**: {grade}

## 页面基本信息
- **页面标题**: {self.test_results['page_structure_analysis'].get('basic_info', {}).get('title', 'N/A')}
- **页面元素统计**: 按钮{self.test_results['page_structure_analysis'].get('elements', {}).get('buttons', 0)}个, 链接{self.test_results['page_structure_analysis'].get('elements', {}).get('links', 0)}个

## 用户旅程测试结果
"""
        
        for journey in self.test_results['user_journey_tests']:
            status = '✅ 成功' if journey.get('success') else '❌ 失败'
            report_content += f"- **{journey['name']}**: {status}\n"
        
        # 添加性能指标
        perf_analysis = self.test_results['performance_analysis']
        if perf_analysis:
            report_content += f"""
## 性能指标
- **页面加载时间**: {perf_analysis.get('page_load_time', 0):.2f}秒
- **性能评级**: {perf_analysis.get('performance_grade', 'N/A')}
"""
        
        # 添加发现的问题
        issues = self.test_results['issues_discovered']
        if issues:
            report_content += f"\n## 发现的问题 ({len(issues)}个)\n\n"
            for issue in issues[:10]:  # 只显示前10个问题
                report_content += f"- **{issue['type']}** ({issue['severity'].upper()}): {issue['message']}\n"
        
        # 添加改进建议
        recommendations = self.test_results['recommendations']
        if recommendations:
            report_content += f"\n## 改进建议 ({len(recommendations)}条)\n\n"
            for rec in recommendations:
                report_content += f"### {rec['issue']} ({rec['priority'].upper()})\n"
                report_content += f"- **类别**: {rec['category']}\n"
                report_content += f"- **建议**: {rec['recommendation']}\n"
                report_content += f"- **影响**: {rec['impact']}\n\n"
        
        # 添加评分详情
        score_breakdown = self.test_results.get('score_breakdown', {})
        if score_breakdown:
            report_content += f"\n## 评分详情\n\n"
            for component, score in score_breakdown.items():
                report_content += f"- **{component}**: {score:.1f}/100\n"
        
        report_content += f"\n## 测试统计\n"
        report_content += f"- **截图数量**: {len(self.test_results['screenshots'])}张\n"
        report_content += f"- **测试持续时间**: {self.test_results['session_info'].get('duration', 0):.1f}秒\n"
        
        report_content += f"\n---\n**报告生成时间**: {datetime.now().isoformat()}\n"
        report_content += f"**测试工具**: Puppeteer MCP + Playwright (专注前端模式)\n"
        
        # 保存Markdown报告
        md_file = f"mcp/puppeteer/focused_frontend_test_{self.session_id}.md"
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
    
    def _print_summary(self):
        """打印测试总结"""
        ux_score = self.test_results['user_experience_score']
        issues_count = len(self.test_results['issues_discovered'])
        recommendations_count = len(self.test_results['recommendations'])
        screenshots_count = len(self.test_results['screenshots'])
        
        print("\n" + "="*80)
        print("专注前端深度用户体验测试完成")
        print("="*80)
        print(f"用户体验评分: {ux_score}/100")
        print(f"发现问题: {issues_count}个")
        print(f"改进建议: {recommendations_count}条")
        print(f"截图数量: {screenshots_count}张")
        
        # 显示评级
        if ux_score >= 90:
            grade = "优秀 ⭐⭐⭐⭐⭐"
        elif ux_score >= 80:
            grade = "良好 ⭐⭐⭐⭐☆"
        elif ux_score >= 70:
            grade = "一般 ⭐⭐⭐☆☆"
        elif ux_score >= 60:
            grade = "较差 ⭐⭐☆☆☆"
        else:
            grade = "很差 ⭐☆☆☆☆"
        
        print(f"评级: {grade}")
        print("="*80)


async def main():
    """主函数"""
    print("启动专注前端深度用户体验测试")
    print("目标: 深度分析前端用户体验，发现改进机会")
    print("-" * 60)
    
    tester = FocusedFrontendTest()
    await tester.run_focused_test()
    
    print("\n专注前端测试完成！请查看生成的详细报告和截图。")


if __name__ == "__main__":
    asyncio.run(main())
