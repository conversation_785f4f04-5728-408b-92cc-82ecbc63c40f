{"session_info": {"session_id": "1754443315", "start_time": "2025-08-06T09:21:55.600771", "test_type": "真实用户深度体验测试", "platform_url": "http://localhost:5173", "user_persona": "量化投资新手用户", "end_time": "2025-08-06T09:22:07.109474", "total_duration": 11.508703}, "user_scenarios": [{"name": "新用户首次访问", "start_time": "2025-08-06T09:22:00.205025", "steps": [], "issues_found": [], "user_experience_rating": 0, "error": "Page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5173/\nCall log:\n  - navigating to \"http://localhost:5173/\", waiting until \"networkidle\"\n", "end_time": "2025-08-06T09:22:03.735087"}, {"name": "平台功能探索", "start_time": "2025-08-06T09:22:03.739980", "features_tested": [], "issues_found": [], "error": "Page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5173/\nCall log:\n  - navigating to \"http://localhost:5173/\", waiting until \"load\"\n", "end_time": "2025-08-06T09:22:07.091995"}, {"name": "核心工作流测试", "start_time": "2025-08-06T09:22:07.095754", "workflows_tested": [], "completion_rate": 0, "end_time": "2025-08-06T09:22:07.095759"}, {"name": "性能测试", "start_time": "2025-08-06T09:22:07.098952", "performance_metrics": {}, "end_time": "2025-08-06T09:22:07.098956"}, {"name": "错误处理测试", "start_time": "2025-08-06T09:22:07.101864", "error_scenarios": [], "end_time": "2025-08-06T09:22:07.101869"}, {"name": "可访问性测试", "start_time": "2025-08-06T09:22:07.105681", "accessibility_score": 0, "end_time": "2025-08-06T09:22:07.105692"}], "discovered_issues": [], "user_experience_metrics": {}, "performance_data": {}, "accessibility_audit": {}, "security_findings": [], "recommendations": [{"priority": "medium", "category": "用户体验", "description": "改进用户界面和交互设计，提升整体用户体验", "impact": "medium"}], "overall_score": 58.333333333333336}