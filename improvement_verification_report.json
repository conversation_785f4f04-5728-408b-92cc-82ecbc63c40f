{"improvement_score": 70.58823529411765, "total_checks": 17, "passed_checks": 12, "html_pages": {"index.html": true, "trading-terminal.html": true, "monitoring-dashboard.html": true}, "backend_files": {"backend/app/api/v1/storage.py": true, "backend/app/api/v1/compression.py": true, "backend/app/services/performance_optimization_service.py": true}, "frontend_files": {"frontend/src/components/common/EnhancedInteraction.vue": true, "frontend/src/services/performance-monitor.service.ts": true, "frontend/index.html": true}, "api_routes": {"/health": {"status": null, "available": false}, "/api/v1/storage/stats": {"status": null, "available": false}, "/api/v1/compression/stats": {"status": null, "available": false}, "/api/v1/auth/login": {"status": null, "available": false}, "/api/v1/monitoring/system": {"status": null, "available": false}}, "console_fixed": true, "performance_optimized": true, "interaction_enhanced": true}