"""
API v1 路由模块
集成所有API路由
"""

from fastapi import APIRouter

from app.api.captcha import router as captcha_router

from .auth import router as auth_router
from .backtest import router as backtest_router
from .ctp import router as ctp_router
from .ctp_websocket import router as ctp_websocket_router
from .market import router as market_router
from .market_data import router as market_data_router
from .monitoring import router as monitoring_router
from .performance import router as performance_router
from .risk import router as risk_router
from .security import router as security_router
from .security_dashboard import router as security_dashboard_router
from .strategy import router as strategy_router
from .strategy_files import router as strategy_files_router
from .trading import router as trading_router
from .websocket import router as websocket_router
from .ws_market import router as ws_market_router
from .error_monitoring import router as error_monitoring_router
from .strategy_optimization import router as strategy_optimization_router
from .historical_data import router as historical_data_router
from .trading_terminal import router as trading_terminal_router
from .order_management import router as order_management_router
from .strategy_development import router as strategy_development_router
from .akshare_api import router as akshare_router
from .backtest_integrated import router as backtest_integrated_router
from .simulated_trading import router as simulated_trading_router
from .account_management import router as account_management_router
from .enhanced_market import router as enhanced_market_router
from .storage import router as storage_router
from .compression import router as compression_router

# 创建API v1路由器
api_router = APIRouter()

# 注册所有路由
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
api_router.include_router(market_router, prefix="/market", tags=["行情"])
api_router.include_router(market_data_router, prefix="/market", tags=["市场数据"])
api_router.include_router(trading_router, prefix="/trading", tags=["交易"])
api_router.include_router(strategy_router, prefix="/strategy", tags=["策略"])
api_router.include_router(strategy_files_router, tags=["策略文件管理"])
api_router.include_router(backtest_router, prefix="/backtest", tags=["回测"])
api_router.include_router(risk_router, prefix="/risk", tags=["风控"])
api_router.include_router(ctp_router, tags=["CTP交易接口"])
api_router.include_router(ctp_websocket_router, tags=["CTP WebSocket"])
api_router.include_router(monitoring_router, prefix="/monitoring", tags=["监控告警"])
api_router.include_router(performance_router, tags=["性能优化"])
api_router.include_router(security_router, tags=["安全管理"])
api_router.include_router(security_dashboard_router, tags=["安全监控仪表板"])
api_router.include_router(captcha_router, prefix="/captcha", tags=["验证码"])
api_router.include_router(websocket_router, prefix="/ws", tags=["WebSocket"])
api_router.include_router(ws_market_router, tags=["WebSocket市场数据"])
api_router.include_router(error_monitoring_router, tags=["错误监控"])
api_router.include_router(historical_data_router, prefix="/historical", tags=["历史数据"])
api_router.include_router(storage_router, prefix="/storage", tags=["存储管理"])
api_router.include_router(compression_router, prefix="/compression", tags=["压缩优化"])

# 添加调试端点
@api_router.get("/debug/routes")
async def debug_routes():
    """调试端点：显示所有注册的路由"""
    routes = []
    for route in api_router.routes:
        routes.append({
            "path": route.path,
            "methods": getattr(route, 'methods', ['WebSocket'] if hasattr(route, 'endpoint') else []),
            "name": getattr(route, 'name', 'unknown')
        })
    return {"routes": routes}

__all__ = ["api_router"]


# 注册额外的功能模块路由
api_router.include_router(enhanced_market_router, prefix='/market/enhanced', tags=['增强版市场数据'])
api_router.include_router(trading_terminal_router, prefix='/terminal', tags=['交易终端'])
api_router.include_router(order_management_router, prefix='/orders', tags=['订单管理'])
api_router.include_router(strategy_development_router, prefix='/strategy-dev', tags=['策略开发'])
api_router.include_router(akshare_router, prefix='/datasources', tags=['AkShare数据源'])
api_router.include_router(backtest_integrated_router, prefix='/backtest-integrated', tags=['集成回测系统'])
api_router.include_router(simulated_trading_router, prefix='/simulated', tags=['模拟交易'])
api_router.include_router(account_management_router, prefix='/account', tags=['账户管理'])
api_router.include_router(strategy_optimization_router, prefix='/optimization', tags=['策略优化'])