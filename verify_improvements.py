#!/usr/bin/env python3
"""
验证项目改进效果
检查修复的问题和新增的功能
"""
import os
import sys
import json
import requests
from pathlib import Path
import time

def check_file_exists(file_path):
    """检查文件是否存在"""
    return Path(file_path).exists()

def check_api_endpoint(url, timeout=5):
    """检查API端点是否可用"""
    try:
        response = requests.get(url, timeout=timeout)
        return response.status_code, response.text[:100]
    except Exception as e:
        return None, str(e)

def verify_html_pages():
    """验证HTML页面是否创建"""
    pages = [
        'index.html',
        'trading-terminal.html', 
        'monitoring-dashboard.html'
    ]
    
    results = {}
    for page in pages:
        exists = check_file_exists(page)
        results[page] = exists
        print(f"{'✅' if exists else '❌'} {page}: {'存在' if exists else '缺失'}")
    
    return results

def verify_backend_files():
    """验证后端文件是否创建"""
    files = [
        'backend/app/api/v1/storage.py',
        'backend/app/api/v1/compression.py',
        'backend/app/services/performance_optimization_service.py'
    ]
    
    results = {}
    for file_path in files:
        exists = check_file_exists(file_path)
        results[file_path] = exists
        print(f"{'✅' if exists else '❌'} {file_path}: {'存在' if exists else '缺失'}")
    
    return results

def verify_frontend_files():
    """验证前端文件是否更新"""
    files = [
        'frontend/src/components/common/EnhancedInteraction.vue',
        'frontend/src/services/performance-monitor.service.ts',
        'frontend/index.html'
    ]
    
    results = {}
    for file_path in files:
        exists = check_file_exists(file_path)
        results[file_path] = exists
        print(f"{'✅' if exists else '❌'} {file_path}: {'存在' if exists else '缺失'}")
    
    return results

def verify_api_routes():
    """验证API路由是否可用"""
    base_url = "http://localhost:8000"
    endpoints = [
        "/health",
        "/api/v1/storage/stats",
        "/api/v1/compression/stats",
        "/api/v1/auth/login",
        "/api/v1/monitoring/system"
    ]
    
    results = {}
    print("\n🔍 检查API端点...")
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        status, response = check_api_endpoint(url)
        results[endpoint] = {
            'status': status,
            'available': status is not None and status < 500
        }
        
        if status:
            print(f"{'✅' if status < 500 else '❌'} {endpoint}: HTTP {status}")
        else:
            print(f"❌ {endpoint}: 连接失败")
    
    return results

def check_console_errors():
    """检查控制台错误修复"""
    html_file = 'frontend/index.html'
    if not check_file_exists(html_file):
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否移除了有问题的X-Frame-Options meta标签
    has_xframe_meta = 'http-equiv="X-Frame-Options"' in content
    
    # 检查是否修复了预加载配置
    has_proper_preload = 'crossorigin="anonymous"' in content
    
    print(f"{'✅' if not has_xframe_meta else '❌'} X-Frame-Options meta标签: {'已移除' if not has_xframe_meta else '仍存在'}")
    print(f"{'✅' if has_proper_preload else '❌'} 预加载配置: {'已修复' if has_proper_preload else '未修复'}")
    
    return not has_xframe_meta and has_proper_preload

def check_performance_optimizations():
    """检查性能优化功能"""
    perf_service = 'frontend/src/services/performance-monitor.service.ts'
    if not check_file_exists(perf_service):
        return False
    
    with open(perf_service, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否添加了性能优化方法
    optimizations = [
        'checkPerformanceThresholds',
        'triggerOptimization',
        'enableRequestDeduplication',
        'enableRequestCaching',
        'optimizeAPIRequests'
    ]
    
    results = {}
    for opt in optimizations:
        exists = opt in content
        results[opt] = exists
        print(f"{'✅' if exists else '❌'} {opt}: {'已添加' if exists else '缺失'}")
    
    return all(results.values())

def check_enhanced_interaction():
    """检查增强交互组件"""
    component_file = 'frontend/src/components/common/EnhancedInteraction.vue'
    if not check_file_exists(component_file):
        return False
    
    with open(component_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键功能
    features = [
        'quick-actions-panel',
        'smart-search',
        'floating-actions',
        'notifications-panel',
        'shortcuts-panel'
    ]
    
    results = {}
    for feature in features:
        exists = feature in content
        results[feature] = exists
        print(f"{'✅' if exists else '❌'} {feature}: {'已实现' if exists else '缺失'}")
    
    return all(results.values())

def generate_improvement_report():
    """生成改进报告"""
    print("=" * 60)
    print("🎯 项目改进验证报告")
    print("=" * 60)
    
    # 验证各个方面的改进
    print("\n📄 HTML页面创建:")
    html_results = verify_html_pages()
    
    print("\n🔧 后端文件创建:")
    backend_results = verify_backend_files()
    
    print("\n🎨 前端文件更新:")
    frontend_results = verify_frontend_files()
    
    print("\n🌐 API路由验证:")
    api_results = verify_api_routes()
    
    print("\n🐛 控制台错误修复:")
    console_fixed = check_console_errors()
    
    print("\n⚡ 性能优化功能:")
    perf_optimized = check_performance_optimizations()
    
    print("\n🎮 增强交互组件:")
    interaction_enhanced = check_enhanced_interaction()
    
    # 计算总体改进分数
    total_checks = 0
    passed_checks = 0
    
    # HTML页面
    total_checks += len(html_results)
    passed_checks += sum(html_results.values())
    
    # 后端文件
    total_checks += len(backend_results)
    passed_checks += sum(backend_results.values())
    
    # 前端文件
    total_checks += len(frontend_results)
    passed_checks += sum(frontend_results.values())
    
    # API可用性
    total_checks += len(api_results)
    passed_checks += sum(1 for r in api_results.values() if r.get('available', False))
    
    # 其他检查
    total_checks += 3
    passed_checks += sum([console_fixed, perf_optimized, interaction_enhanced])
    
    improvement_score = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
    
    print("\n" + "=" * 60)
    print(f"📊 总体改进评分: {improvement_score:.1f}% ({passed_checks}/{total_checks})")
    print("=" * 60)
    
    # 生成改进建议
    print("\n💡 改进建议:")
    if improvement_score >= 90:
        print("🎉 优秀！项目改进效果显著，所有主要问题已解决")
    elif improvement_score >= 70:
        print("👍 良好！大部分问题已解决，建议继续完善细节")
    elif improvement_score >= 50:
        print("⚠️  一般！部分问题已解决，需要继续改进")
    else:
        print("❌ 需要改进！大部分问题仍未解决")
    
    # 具体建议
    if not all(html_results.values()):
        print("- 完善缺失的HTML页面")
    
    if not all(backend_results.values()):
        print("- 完善后端API实现")
    
    if not console_fixed:
        print("- 修复剩余的控制台错误")
    
    if not perf_optimized:
        print("- 完善性能优化功能")
    
    if not interaction_enhanced:
        print("- 增强用户交互体验")
    
    return {
        'improvement_score': improvement_score,
        'total_checks': total_checks,
        'passed_checks': passed_checks,
        'html_pages': html_results,
        'backend_files': backend_results,
        'frontend_files': frontend_results,
        'api_routes': api_results,
        'console_fixed': console_fixed,
        'performance_optimized': perf_optimized,
        'interaction_enhanced': interaction_enhanced
    }

if __name__ == "__main__":
    try:
        report = generate_improvement_report()
        
        # 保存报告到文件
        with open('improvement_verification_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📋 详细报告已保存到: improvement_verification_report.json")
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        sys.exit(1)
