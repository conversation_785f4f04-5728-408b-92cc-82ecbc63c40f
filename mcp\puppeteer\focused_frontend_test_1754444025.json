{"session_info": {"session_id": "1754444025", "start_time": "2025-08-06T09:33:45.626497", "test_type": "专注前端深度用户体验测试", "platform_url": "http://localhost:5173", "end_time": "2025-08-06T09:34:19.699166", "duration": 34.072669}, "page_structure_analysis": {"basic_info": {"title": "仪表盘 - 量化投资平台", "url": "http://localhost:5173/", "load_timestamp": "2025-08-06T09:33:48.780958"}, "elements": {"buttons": 9, "links": 0, "inputs": 5, "forms": 0, "images": 1, "headings": 6, "paragraphs": 3, "divs": 107, "spans": 31, "navigation_containers": 1, "menu_containers": 0, "chart_elements": 32, "table_elements": 0}, "layout": {"page_dimensions": {"width": 1920, "height": 1169, "viewportWidth": 1920, "viewportHeight": 1080}, "responsive_elements": 0, "layout_containers": 0, "has_responsive_design": false}, "content": {"text_length": 385, "has_meaningful_content": true, "heading_structure": [{"level": "H1", "text": "📊 投资仪表盘"}, {"level": "H3", "text": "📊 投资组合趋势"}, {"level": "H3", "text": "📰 市场资讯"}, {"level": "H4", "text": "市场分析：科技股表现强劲"}, {"level": "H4", "text": "央行政策解读"}, {"level": "H4", "text": "新能源汽车销量创新高"}], "main_content_areas": 1}}, "user_journey_tests": [{"name": "新用户首次访问", "start_time": "2025-08-06T09:33:48.781459", "steps": [{"step": "页面加载", "success": true, "description": "页面成功加载"}, {"step": "功能发现", "success": false, "description": "发现0个主要功能区域"}, {"step": "导航发现", "success": false, "description": "发现0个导航链接"}], "success": true, "issues": ["未发现明显的主要功能区域", "未发现明显的导航元素"], "end_time": "2025-08-06T09:33:54.313639"}, {"name": "功能探索", "start_time": "2025-08-06T09:33:54.313647", "steps": [{"step": "功能交互测试", "success": true, "description": "成功交互1个功能元素"}], "success": true, "features_found": [{"selector": "button:not([type=\"submit\"])", "clickable": true}], "end_time": "2025-08-06T09:34:00.408742"}, {"name": "导航测试", "start_time": "2025-08-06T09:34:00.408751", "steps": [{"step": "导航链接测试", "success": false, "description": "成功测试0个导航路径"}], "success": true, "navigation_paths": [], "end_time": "2025-08-06T09:34:00.414300"}], "interaction_tests": {"button_interactions": [{"index": 0, "text": "", "hoverable": true}, {"index": 1, "text": "", "hoverable": true}, {"index": 2, "text": "", "hoverable": true}, {"index": 3, "text": "", "hoverable": true}, {"index": 4, "text": "今日", "hoverable": true}], "form_interactions": [], "hover_effects": [], "keyboard_navigation": []}, "visual_analysis": {"body_styles": {"backgroundColor": "rgb(245, 247, 250)", "color": "rgb(0, 0, 0)", "fontFamily": "\"Noto Sans SC\"", "fontSize": "16px"}, "heading_hierarchy": true, "has_visual_structure": true}, "performance_analysis": {"page_load_time": 1.134944200515747, "browser_metrics": {"domContentLoaded": 0.10000000009313226, "loadComplete": 0.10000000009313226, "firstPaint": 0, "firstContentfulPaint": 0}, "performance_grade": "B"}, "accessibility_analysis": {"images_with_alt": 1, "images_without_alt": 0, "form_labels": 5, "heading_structure": 6, "accessibility_score": 100}, "issues_discovered": [{"type": "console_warning", "severity": "medium", "message": "The resource http://localhost:5173/src/App.vue was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-06T09:33:51.976241", "source": "console"}, {"type": "console_warning", "severity": "medium", "message": "The resource http://localhost:5173/src/main.ts was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.", "timestamp": "2025-08-06T09:33:51.976596", "source": "console"}, {"type": "console_warning", "severity": "medium", "message": "⚠️ 性能警告: web_vitals_fid 超过阈值 100, 当前值: 7412.299999999814", "timestamp": "2025-08-06T09:33:56.198101", "source": "console"}], "user_experience_score": 83.8, "recommendations": [], "screenshots": [{"filename": "test_1754444025_001_homepage_loaded_1754444028.png", "path": "mcp\\puppeteer\\screenshots_focused\\test_1754444025_001_homepage_loaded_1754444028.png", "description": "主页加载完成", "timestamp": "2025-08-06T09:33:48.542317"}, {"filename": "test_1754444025_002_first_impression_1754444031.png", "path": "mcp\\puppeteer\\screenshots_focused\\test_1754444025_002_first_impression_1754444031.png", "description": "新用户第一印象", "timestamp": "2025-08-06T09:33:54.305461"}, {"filename": "test_1754444025_003_feature_click_1754444037.png", "path": "mcp\\puppeteer\\screenshots_focused\\test_1754444025_003_feature_click_1754444037.png", "description": "点击button:not([type=\"submit\"])元素", "timestamp": "2025-08-06T09:34:00.403798"}, {"filename": "test_1754444025_004_visual_analysis_1754444056.png", "path": "mcp\\puppeteer\\screenshots_focused\\test_1754444025_004_visual_analysis_1754444056.png", "description": "视觉布局分析", "timestamp": "2025-08-06T09:34:18.510345"}], "score_breakdown": {"page_structure": 60, "user_journeys": 100.0, "interactions": 80, "performance": 85, "accessibility": 100}}