#!/usr/bin/env python3
"""
高级MCP真实用户测试
使用实际的MCP工具进行浏览器自动化和文件系统操作

这个脚本将：
1. 启动实际的MCP服务器
2. 使用BrowserTools MCP进行浏览器自动化
3. 使用FileSystem MCP进行文件操作
4. 模拟真实用户的完整使用流程
5. 生成详细的问题报告和截图
"""

import asyncio
import json
import time
import os
import sys
import logging
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import requests

# 尝试导入mcp-use库
try:
    from mcp_use import MCPClient
    MCP_AVAILABLE = True
except ImportError:
    print("⚠️ mcp-use库未安装，将使用模拟模式")
    MCP_AVAILABLE = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp/mcp_testing/advanced_mcp_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdvancedMCPRealUserTester:
    """高级MCP真实用户测试器"""
    
    def __init__(self):
        self.session_id = str(int(time.time()))
        self.test_results = {
            'session_id': self.session_id,
            'start_time': datetime.now().isoformat(),
            'mcp_tools_used': [],
            'user_scenarios': [],
            'issues_discovered': [],
            'screenshots_taken': [],
            'performance_metrics': {},
            'user_experience_rating': 0
        }
        
        # 目录设置
        self.screenshots_dir = Path('mcp/mcp_testing/screenshots')
        self.reports_dir = Path('mcp/mcp_testing/reports')
        self.screenshots_dir.mkdir(exist_ok=True)
        self.reports_dir.mkdir(exist_ok=True)
        
        # MCP客户端
        self.browser_client = None
        self.filesystem_client = None
        
        # 平台配置
        self.platform_url = "http://localhost:5173"
        self.backend_url = "http://localhost:8000"
        
    async def run_advanced_test(self):
        """运行高级MCP测试"""
        logger.info(f"🚀 开始高级MCP真实用户测试 - 会话ID: {self.session_id}")
        
        try:
            # 步骤1: 初始化MCP工具
            await self._initialize_mcp_tools()
            
            # 步骤2: 启动平台服务
            await self._start_platform_services()
            
            # 步骤3: 真实用户场景测试
            await self._run_real_user_scenarios()
            
            # 步骤4: 性能和可用性测试
            await self._run_performance_tests()
            
            # 步骤5: 问题发现和分析
            await self._discover_and_analyze_issues()
            
            # 步骤6: 生成综合报告
            await self._generate_comprehensive_report()
            
        except Exception as e:
            logger.error(f"❌ 测试过程中发生错误: {e}")
            self.test_results['error'] = str(e)
        finally:
            await self._cleanup_mcp_tools()
    
    async def _initialize_mcp_tools(self):
        """初始化MCP工具"""
        logger.info("🔧 初始化MCP工具...")
        
        if not MCP_AVAILABLE:
            logger.warning("⚠️ MCP库不可用，使用模拟模式")
            return
        
        try:
            # 初始化BrowserTools MCP客户端
            self.browser_client = MCPClient()
            browser_server_path = "mcp/browser-tools-mcp/browser-tools-mcp"
            if os.path.exists(browser_server_path):
                await self.browser_client.connect("stdio", command=["node", f"{browser_server_path}/index.js"])
                self.test_results['mcp_tools_used'].append('BrowserTools MCP')
                logger.info("✅ BrowserTools MCP已连接")
            
            # 初始化FileSystem MCP客户端
            self.filesystem_client = MCPClient()
            fs_server_path = "mcp/servers/src/filesystem"
            if os.path.exists(fs_server_path):
                await self.filesystem_client.connect("stdio", command=["node", f"{fs_server_path}/index.js"])
                self.test_results['mcp_tools_used'].append('FileSystem MCP')
                logger.info("✅ FileSystem MCP已连接")
                
        except Exception as e:
            logger.error(f"❌ MCP工具初始化失败: {e}")
            # 继续使用模拟模式
    
    async def _start_platform_services(self):
        """启动平台服务"""
        logger.info("🚀 启动平台服务...")
        
        # 检查服务是否已运行
        frontend_running = await self._check_service(self.platform_url)
        backend_running = await self._check_service(f"{self.backend_url}/health")
        
        if not frontend_running:
            logger.info("🌐 启动前端服务...")
            await self._start_frontend_service()
        
        if not backend_running:
            logger.info("🔧 启动后端服务...")
            await self._start_backend_service()
        
        # 等待服务启动
        await asyncio.sleep(15)
        
        # 再次检查服务状态
        frontend_running = await self._check_service(self.platform_url)
        backend_running = await self._check_service(f"{self.backend_url}/health")
        
        self.test_results['services_status'] = {
            'frontend_running': frontend_running,
            'backend_running': backend_running
        }
        
        logger.info(f"📊 服务状态 - 前端: {'✅' if frontend_running else '❌'}, 后端: {'✅' if backend_running else '❌'}")
    
    async def _check_service(self, url: str) -> bool:
        """检查服务是否运行"""
        try:
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    async def _start_frontend_service(self):
        """启动前端服务"""
        try:
            if os.path.exists('frontend'):
                subprocess.Popen(['npm', 'run', 'dev'], cwd='frontend', shell=True)
                logger.info("🌐 前端服务启动命令已执行")
        except Exception as e:
            logger.error(f"❌ 前端服务启动失败: {e}")
    
    async def _start_backend_service(self):
        """启动后端服务"""
        try:
            if os.path.exists('backend/app.py'):
                subprocess.Popen(['python', 'app.py'], cwd='backend', shell=True)
                logger.info("🔧 后端服务启动命令已执行")
            elif os.path.exists('backend/main.py'):
                subprocess.Popen(['python', 'main.py'], cwd='backend', shell=True)
                logger.info("🔧 后端服务启动命令已执行")
        except Exception as e:
            logger.error(f"❌ 后端服务启动失败: {e}")
    
    async def _run_real_user_scenarios(self):
        """运行真实用户场景测试"""
        logger.info("👤 运行真实用户场景测试...")
        
        scenarios = [
            "新用户首次访问",
            "浏览市场数据",
            "查看交易终端",
            "探索策略中心",
            "检查投资组合",
            "测试风险管理功能"
        ]
        
        for scenario in scenarios:
            scenario_result = await self._run_user_scenario(scenario)
            self.test_results['user_scenarios'].append(scenario_result)
    
    async def _run_user_scenario(self, scenario_name: str) -> Dict[str, Any]:
        """运行单个用户场景"""
        logger.info(f"🎭 运行场景: {scenario_name}")
        
        scenario_result = {
            'name': scenario_name,
            'start_time': datetime.now().isoformat(),
            'success': False,
            'issues': [],
            'screenshots': [],
            'performance': {}
        }
        
        try:
            if scenario_name == "新用户首次访问":
                result = await self._test_first_visit()
            elif scenario_name == "浏览市场数据":
                result = await self._test_market_data_browsing()
            elif scenario_name == "查看交易终端":
                result = await self._test_trading_terminal()
            elif scenario_name == "探索策略中心":
                result = await self._test_strategy_center()
            elif scenario_name == "检查投资组合":
                result = await self._test_portfolio_view()
            elif scenario_name == "测试风险管理功能":
                result = await self._test_risk_management()
            else:
                result = await self._test_generic_scenario(scenario_name)
            
            scenario_result.update(result)
            scenario_result['success'] = True
            
        except Exception as e:
            scenario_result['issues'].append(f"场景执行失败: {str(e)}")
            logger.error(f"❌ 场景 {scenario_name} 执行失败: {e}")
        
        scenario_result['end_time'] = datetime.now().isoformat()
        return scenario_result
    
    async def _test_first_visit(self) -> Dict[str, Any]:
        """测试新用户首次访问"""
        result = {
            'page_loaded': False,
            'load_time': 0,
            'navigation_visible': False,
            'content_meaningful': False
        }
        
        start_time = time.time()
        
        # 使用BrowserTools MCP进行实际测试
        if self.browser_client:
            try:
                # 导航到主页
                await self.browser_client.call_tool("navigate", {"url": self.platform_url})
                
                # 等待页面加载
                await asyncio.sleep(3)
                
                # 截图
                screenshot = await self.browser_client.call_tool("take_screenshot", {})
                if screenshot:
                    screenshot_path = f"first_visit_{self.session_id}.png"
                    await self._save_screenshot(screenshot, screenshot_path)
                    result['screenshots'] = [screenshot_path]
                
                # 检查页面元素
                page_title = await self.browser_client.call_tool("get_page_title", {})
                if page_title and ('量化' in page_title or 'quant' in page_title.lower()):
                    result['content_meaningful'] = True
                
                result['page_loaded'] = True
                
            except Exception as e:
                logger.error(f"BrowserTools MCP测试失败: {e}")
        
        # 备用HTTP测试
        if not result['page_loaded']:
            try:
                response = requests.get(self.platform_url, timeout=10)
                result['page_loaded'] = response.status_code == 200
                if response.text and len(response.text) > 1000:
                    result['content_meaningful'] = True
            except Exception as e:
                logger.error(f"HTTP测试失败: {e}")
        
        result['load_time'] = time.time() - start_time
        return result

    async def _test_market_data_browsing(self) -> Dict[str, Any]:
        """测试市场数据浏览"""
        result = {
            'market_page_accessible': False,
            'data_charts_visible': False,
            'real_time_updates': False
        }

        if self.browser_client:
            try:
                # 导航到市场数据页面
                market_url = f"{self.platform_url}/#/market"
                await self.browser_client.call_tool("navigate", {"url": market_url})
                await asyncio.sleep(3)

                # 截图
                screenshot = await self.browser_client.call_tool("take_screenshot", {})
                if screenshot:
                    screenshot_path = f"market_data_{self.session_id}.png"
                    await self._save_screenshot(screenshot, screenshot_path)
                    result['screenshots'] = [screenshot_path]

                # 检查图表元素
                charts = await self.browser_client.call_tool("find_elements", {"selector": "canvas, .chart, .echarts"})
                result['data_charts_visible'] = len(charts) > 0 if charts else False

                result['market_page_accessible'] = True

            except Exception as e:
                logger.error(f"市场数据测试失败: {e}")

        return result

    async def _test_trading_terminal(self) -> Dict[str, Any]:
        """测试交易终端"""
        result = {
            'terminal_accessible': False,
            'trading_forms_present': False,
            'order_book_visible': False
        }

        if self.browser_client:
            try:
                # 导航到交易终端
                trading_url = f"{self.platform_url}/#/trading"
                await self.browser_client.call_tool("navigate", {"url": trading_url})
                await asyncio.sleep(3)

                # 截图
                screenshot = await self.browser_client.call_tool("take_screenshot", {})
                if screenshot:
                    screenshot_path = f"trading_terminal_{self.session_id}.png"
                    await self._save_screenshot(screenshot, screenshot_path)
                    result['screenshots'] = [screenshot_path]

                # 检查交易表单
                forms = await self.browser_client.call_tool("find_elements", {"selector": "form, .trading-form, .order-form"})
                result['trading_forms_present'] = len(forms) > 0 if forms else False

                result['terminal_accessible'] = True

            except Exception as e:
                logger.error(f"交易终端测试失败: {e}")

        return result

    async def _test_strategy_center(self) -> Dict[str, Any]:
        """测试策略中心"""
        result = {
            'strategy_page_accessible': False,
            'strategy_list_visible': False,
            'strategy_creation_available': False
        }

        if self.browser_client:
            try:
                # 导航到策略中心
                strategy_url = f"{self.platform_url}/#/strategy"
                await self.browser_client.call_tool("navigate", {"url": strategy_url})
                await asyncio.sleep(3)

                # 截图
                screenshot = await self.browser_client.call_tool("take_screenshot", {})
                if screenshot:
                    screenshot_path = f"strategy_center_{self.session_id}.png"
                    await self._save_screenshot(screenshot, screenshot_path)
                    result['screenshots'] = [screenshot_path]

                # 检查策略列表
                strategies = await self.browser_client.call_tool("find_elements", {"selector": ".strategy-item, .strategy-card, .strategy-list"})
                result['strategy_list_visible'] = len(strategies) > 0 if strategies else False

                result['strategy_page_accessible'] = True

            except Exception as e:
                logger.error(f"策略中心测试失败: {e}")

        return result

    async def _test_portfolio_view(self) -> Dict[str, Any]:
        """测试投资组合视图"""
        result = {
            'portfolio_accessible': False,
            'holdings_displayed': False,
            'performance_charts': False
        }

        if self.browser_client:
            try:
                # 导航到投资组合
                portfolio_url = f"{self.platform_url}/#/portfolio"
                await self.browser_client.call_tool("navigate", {"url": portfolio_url})
                await asyncio.sleep(3)

                # 截图
                screenshot = await self.browser_client.call_tool("take_screenshot", {})
                if screenshot:
                    screenshot_path = f"portfolio_{self.session_id}.png"
                    await self._save_screenshot(screenshot, screenshot_path)
                    result['screenshots'] = [screenshot_path]

                result['portfolio_accessible'] = True

            except Exception as e:
                logger.error(f"投资组合测试失败: {e}")

        return result

    async def _test_risk_management(self) -> Dict[str, Any]:
        """测试风险管理功能"""
        result = {
            'risk_page_accessible': False,
            'risk_metrics_visible': False,
            'alerts_configured': False
        }

        if self.browser_client:
            try:
                # 导航到风险管理
                risk_url = f"{self.platform_url}/#/risk"
                await self.browser_client.call_tool("navigate", {"url": risk_url})
                await asyncio.sleep(3)

                # 截图
                screenshot = await self.browser_client.call_tool("take_screenshot", {})
                if screenshot:
                    screenshot_path = f"risk_management_{self.session_id}.png"
                    await self._save_screenshot(screenshot, screenshot_path)
                    result['screenshots'] = [screenshot_path]

                result['risk_page_accessible'] = True

            except Exception as e:
                logger.error(f"风险管理测试失败: {e}")

        return result

    async def _test_generic_scenario(self, scenario_name: str) -> Dict[str, Any]:
        """通用场景测试"""
        result = {
            'scenario_completed': False,
            'basic_functionality': False
        }

        # 基本的HTTP可达性测试
        try:
            response = requests.get(self.platform_url, timeout=5)
            result['basic_functionality'] = response.status_code == 200
            result['scenario_completed'] = True
        except Exception as e:
            logger.error(f"通用场景测试失败: {e}")

        return result

    async def _save_screenshot(self, screenshot_data: Any, filename: str):
        """保存截图"""
        try:
            screenshot_path = self.screenshots_dir / filename

            # 如果screenshot_data是base64编码的图片
            if isinstance(screenshot_data, dict) and 'data' in screenshot_data:
                import base64
                with open(screenshot_path, 'wb') as f:
                    f.write(base64.b64decode(screenshot_data['data']))

            self.test_results['screenshots_taken'].append(str(screenshot_path))
            logger.info(f"📸 截图已保存: {filename}")

        except Exception as e:
            logger.error(f"❌ 截图保存失败: {e}")

    async def _run_performance_tests(self):
        """运行性能测试"""
        logger.info("⚡ 运行性能测试...")

        performance_metrics = {
            'page_load_times': [],
            'api_response_times': [],
            'memory_usage': 'unknown',
            'cpu_usage': 'unknown'
        }

        # 测试页面加载时间
        test_pages = [
            self.platform_url,
            f"{self.platform_url}/#/market",
            f"{self.platform_url}/#/trading",
            f"{self.platform_url}/#/strategy"
        ]

        for page_url in test_pages:
            start_time = time.time()
            try:
                response = requests.get(page_url, timeout=10)
                load_time = time.time() - start_time
                if response.status_code == 200:
                    performance_metrics['page_load_times'].append({
                        'url': page_url,
                        'load_time': load_time,
                        'status': 'success'
                    })
                else:
                    performance_metrics['page_load_times'].append({
                        'url': page_url,
                        'load_time': load_time,
                        'status': 'failed',
                        'status_code': response.status_code
                    })
            except Exception as e:
                performance_metrics['page_load_times'].append({
                    'url': page_url,
                    'load_time': time.time() - start_time,
                    'status': 'error',
                    'error': str(e)
                })

        # 测试API响应时间
        api_endpoints = [
            f"{self.backend_url}/health",
            f"{self.backend_url}/api/market/data",
            f"{self.backend_url}/api/user/profile"
        ]

        for api_url in api_endpoints:
            start_time = time.time()
            try:
                response = requests.get(api_url, timeout=5)
                response_time = time.time() - start_time
                performance_metrics['api_response_times'].append({
                    'url': api_url,
                    'response_time': response_time,
                    'status': response.status_code
                })
            except Exception as e:
                performance_metrics['api_response_times'].append({
                    'url': api_url,
                    'response_time': time.time() - start_time,
                    'status': 'error',
                    'error': str(e)
                })

        self.test_results['performance_metrics'] = performance_metrics

        # 计算平均性能分数
        avg_load_time = sum(p['load_time'] for p in performance_metrics['page_load_times'] if p['status'] == 'success')
        successful_loads = len([p for p in performance_metrics['page_load_times'] if p['status'] == 'success'])

        if successful_loads > 0:
            avg_load_time = avg_load_time / successful_loads
            if avg_load_time < 1.0:
                performance_score = 95
            elif avg_load_time < 2.0:
                performance_score = 85
            elif avg_load_time < 3.0:
                performance_score = 75
            else:
                performance_score = 60
        else:
            performance_score = 30

        self.test_results['performance_score'] = performance_score
        logger.info(f"⚡ 性能测试完成 - 平均加载时间: {avg_load_time:.2f}s, 性能评分: {performance_score}")

    async def _discover_and_analyze_issues(self):
        """发现和分析问题"""
        logger.info("🔍 发现和分析问题...")

        issues = []

        # 分析服务状态问题
        if not self.test_results.get('services_status', {}).get('frontend_running', False):
            issues.append({
                'severity': 'critical',
                'category': '服务可用性',
                'issue': '前端服务未运行',
                'impact': '用户无法访问平台界面',
                'recommendation': '启动前端开发服务器或检查部署配置'
            })

        if not self.test_results.get('services_status', {}).get('backend_running', False):
            issues.append({
                'severity': 'critical',
                'category': '服务可用性',
                'issue': '后端服务未运行',
                'impact': '平台核心功能无法使用',
                'recommendation': '启动后端API服务器或检查数据库连接'
            })

        # 分析性能问题
        performance_score = self.test_results.get('performance_score', 0)
        if performance_score < 70:
            issues.append({
                'severity': 'medium',
                'category': '性能',
                'issue': '页面加载速度较慢',
                'impact': '影响用户体验和使用效率',
                'recommendation': '优化前端资源加载和后端API响应速度'
            })

        # 分析用户场景问题
        failed_scenarios = [s for s in self.test_results.get('user_scenarios', []) if not s.get('success', False)]
        if failed_scenarios:
            issues.append({
                'severity': 'high',
                'category': '功能可用性',
                'issue': f'{len(failed_scenarios)}个用户场景测试失败',
                'impact': '核心功能可能存在问题',
                'recommendation': '检查相关功能模块的实现和配置'
            })

        self.test_results['issues_discovered'] = issues
        logger.info(f"🔍 问题分析完成 - 发现 {len(issues)} 个问题")

    async def _generate_comprehensive_report(self):
        """生成综合报告"""
        logger.info("📊 生成综合报告...")

        # 计算整体用户体验评分
        ux_score = self._calculate_overall_ux_score()
        self.test_results['user_experience_rating'] = ux_score
        self.test_results['end_time'] = datetime.now().isoformat()

        # 生成JSON报告
        report_file = self.reports_dir / f"advanced_mcp_test_{self.session_id}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)

        # 生成Markdown报告
        await self._generate_markdown_report()

        logger.info(f"📊 综合报告已生成: {report_file}")

        # 打印测试总结
        self._print_test_summary()

    def _calculate_overall_ux_score(self) -> int:
        """计算整体用户体验评分"""
        scores = []

        # 服务可用性评分 (40%权重)
        services = self.test_results.get('services_status', {})
        if services.get('frontend_running') and services.get('backend_running'):
            service_score = 100
        elif services.get('frontend_running') or services.get('backend_running'):
            service_score = 60
        else:
            service_score = 20
        scores.append(('服务可用性', service_score, 0.4))

        # 性能评分 (30%权重)
        performance_score = self.test_results.get('performance_score', 70)
        scores.append(('性能', performance_score, 0.3))

        # 功能可用性评分 (30%权重)
        successful_scenarios = len([s for s in self.test_results.get('user_scenarios', []) if s.get('success', False)])
        total_scenarios = len(self.test_results.get('user_scenarios', []))
        if total_scenarios > 0:
            functionality_score = (successful_scenarios / total_scenarios) * 100
        else:
            functionality_score = 50
        scores.append(('功能可用性', functionality_score, 0.3))

        # 计算加权平均分
        weighted_score = sum(score * weight for _, score, weight in scores)
        return int(weighted_score)

    async def _generate_markdown_report(self):
        """生成Markdown报告"""
        ux_score = self.test_results['user_experience_rating']

        # 确定评级
        if ux_score >= 90:
            grade = "优秀 ⭐⭐⭐⭐⭐"
        elif ux_score >= 80:
            grade = "良好 ⭐⭐⭐⭐☆"
        elif ux_score >= 70:
            grade = "一般 ⭐⭐⭐☆☆"
        elif ux_score >= 60:
            grade = "较差 ⭐⭐☆☆☆"
        else:
            grade = "很差 ⭐☆☆☆☆"

        report_content = f"""# 高级MCP真实用户测试报告

## 测试概述
- **测试会话ID**: {self.session_id}
- **测试开始时间**: {self.test_results['start_time']}
- **测试结束时间**: {self.test_results['end_time']}
- **使用的MCP工具**: {', '.join(self.test_results['mcp_tools_used'])}
- **用户体验评分**: {ux_score}/100
- **整体评级**: {grade}

## 服务状态
- **前端服务**: {'✅ 运行中' if self.test_results.get('services_status', {}).get('frontend_running') else '❌ 未运行'}
- **后端服务**: {'✅ 运行中' if self.test_results.get('services_status', {}).get('backend_running') else '❌ 未运行'}

## 用户场景测试结果
"""

        for scenario in self.test_results.get('user_scenarios', []):
            status = '✅ 成功' if scenario.get('success') else '❌ 失败'
            report_content += f"- **{scenario['name']}**: {status}\n"

        report_content += f"\n## 性能指标\n"
        report_content += f"- **性能评分**: {self.test_results.get('performance_score', 0)}/100\n"

        # 添加发现的问题
        issues = self.test_results.get('issues_discovered', [])
        if issues:
            report_content += f"\n## 发现的问题 ({len(issues)}个)\n\n"
            for issue in issues:
                report_content += f"### {issue['issue']} ({issue['severity'].upper()})\n"
                report_content += f"- **类别**: {issue['category']}\n"
                report_content += f"- **影响**: {issue['impact']}\n"
                report_content += f"- **建议**: {issue['recommendation']}\n\n"

        report_content += f"\n---\n**报告生成时间**: {datetime.now().isoformat()}\n"
        report_content += f"**测试工具**: 高级MCP工具组合\n"

        # 保存Markdown报告
        md_report_file = self.reports_dir / f"advanced_mcp_test_{self.session_id}.md"
        with open(md_report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

    def _print_test_summary(self):
        """打印测试总结"""
        ux_score = self.test_results['user_experience_rating']

        print("\n" + "="*80)
        print("🎯 高级MCP真实用户测试完成")
        print("="*80)
        print(f"📊 用户体验评分: {ux_score}/100")
        print(f"🔧 使用的MCP工具: {', '.join(self.test_results['mcp_tools_used'])}")
        print(f"🎭 测试场景数量: {len(self.test_results.get('user_scenarios', []))}")
        print(f"📸 截图数量: {len(self.test_results.get('screenshots_taken', []))}")
        print(f"⚠️  发现问题数量: {len(self.test_results.get('issues_discovered', []))}")

        # 显示评级
        if ux_score >= 90:
            grade = "优秀 ⭐⭐⭐⭐⭐"
        elif ux_score >= 80:
            grade = "良好 ⭐⭐⭐⭐☆"
        elif ux_score >= 70:
            grade = "一般 ⭐⭐⭐☆☆"
        elif ux_score >= 60:
            grade = "较差 ⭐⭐☆☆☆"
        else:
            grade = "很差 ⭐☆☆☆☆"

        print(f"🏆 整体评级: {grade}")
        print("="*80)

    async def _cleanup_mcp_tools(self):
        """清理MCP工具"""
        logger.info("🧹 清理MCP工具...")

        try:
            if self.browser_client:
                await self.browser_client.disconnect()
            if self.filesystem_client:
                await self.filesystem_client.disconnect()
        except Exception as e:
            logger.error(f"MCP工具清理失败: {e}")


async def main():
    """主函数"""
    print("🚀 启动高级MCP真实用户测试")
    print("🔧 测试工具: BrowserTools MCP + FileSystem MCP + 浏览器自动化")
    print("🎯 测试目标: 深度模拟真实用户使用流程，发现实际问题")
    print("-" * 60)

    tester = AdvancedMCPRealUserTester()
    await tester.run_advanced_test()

    print("\n✅ 高级MCP测试完成！请查看生成的报告和截图。")


if __name__ == "__main__":
    asyncio.run(main())
